{
	"pages": [
		{
			"path": "pages/tabbar/index/index",
			"style": {
				"navigationBarTitleText": "预约租房",
				"enablePullDownRefresh": true
			}
		},{
			"path": "pages/tabbar/my/index",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/tabbar/add/index",
			"style": {
				"navigationBarTitleText": "发布房源"
			}
		},
		 {
		 	"path": "pages/page/index",
		 	"style": {
		 		"navigationBarTitleText": "内容详情"
		 	}
		 },
		 {
		 	"path": "pages/login/index",
		 	"style": {
		 		"navigationBarTitleText": "微信登录",
		 		"navigationBarBackgroundColor": "#667eea",
		 		"navigationBarTextStyle": "white"
		 	}
		 },
		 {
		 	"path": "pages/auth/index",
		 	"style": {
		 		"navigationBarTitleText": "登录/注册",
		 		"navigationBarBackgroundColor": "#667eea",
		 		"navigationBarTextStyle": "white"
		 	}
		 },
		 {
		 	"path": "pages/goods/detail",
		 	"style": {
		 		"navigationBarTitleText": "内容详情",
				"enablePullDownRefresh": true
		 	}
		 },

		 {
		 	"path": "pages/my/fav/index",
		 	"style": {
		 		"navigationBarTitleText": "我的收藏",
				"enablePullDownRefresh": true
		 	}
		 },
		 {
		 	"path": "pages/my/order/index",
		 	"style": {
		 		"navigationBarTitleText": "我的订单",
				"enablePullDownRefresh": true
		 	}
		 },
		 {
		 	"path": "pages/my/order/order",
		 	"style": {
		 		"navigationBarTitleText": "预约订单"
		 	}
		 },
		 {
		 	"path": "pages/my/feedback/index",
		 	"style": {
		 		"navigationBarTitleText": "投诉建议"
		 	}
		 },
		 {
		 	"path": "pages/house/list/index",
		 	"style": {
		 		"navigationBarTitleText": "房源列表",
		 		"enablePullDownRefresh": true,
		 		"navigationStyle": "default"
		 	}
		 },
		 {
		 	"path": "pages/my/publish/index",
		 	"style": {
		 		"navigationBarTitleText": "我的发布",
		 		"enablePullDownRefresh": true
		 	}
		 },
		 {
		 	"path": "pages/map/select/index",
		 	"style": {
		 		"navigationBarTitleText": "选择位置",
		 		"navigationBarBackgroundColor": "#007AFF",
		 		"navigationBarTextStyle": "white"
		 	}
		 }
,
		 {
		 	"path": "pages/userInfo/index",
		 	"style": {
		 		"navigationBarTitleText": "个人信息"
		 	}
		 },
		 {
		 	"path": "pages/userInfo/detail",
		 	"style": {
		 		"navigationBarTitleText": "用户详情"
		 	}
		 }
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "",
		// #ifndef MP-ALIPAY
		"navigationBarBackgroundColor": "#F8F8F8",
		// #endif
		"backgroundColor": "#F8F8F8",
		 "app-plus": {
		      "titleNView": false
		}
	},
	"tabBar": {
		"color": "#6C757D",
		"selectedColor": "#007BFF",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [{
				"pagePath": "pages/tabbar/index/index",
				"iconPath": "static/home.png",
				"selectedIconPath": "static/home_select.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/house/list/index",
				"iconPath": "static/news.png",
				"selectedIconPath": "static/news_select.png",
				"text": "房源"
			},
			{
				"pagePath": "pages/tabbar/add/index",
				"iconPath": "static/fabu.png",
				"selectedIconPath": "static/fabu.png",
				"text": "发布"
			},
			{
				"pagePath": "pages/tabbar/my/index",
				"iconPath": "static/my.png",
				"selectedIconPath": "static/my_select.png",
				"text": "我的"
			}
		]
	}
}
