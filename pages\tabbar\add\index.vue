<template>
	<view class="publish-container">
		<!-- 页面头部 -->
		<view class="page-header">
			<view class="header-content">
				<text class="header-title">🏠 发布房源</text>
				<text class="header-subtitle">填写详细信息，让租客更容易找到您的房源</text>
			</view>
		</view>

		<view class="form-container">
			<!-- 基本信息卡片 -->
			<view class="form-card modern-card">
				<view class="card-header gradient-header">
					<view class="header-icon">🏘️</view>
					<view class="header-text">
						<text class="card-title">基本信息</text>
						<text class="card-subtitle">选择房源的基本属性</text>
					</view>
				</view>
				<view class="card-content">
					<view class="form-group modern-group">
						<view class="group-header">
							<text class="group-icon">🏠</text>
							<text class="group-label">房型类型</text>
						</view>
						<view class="selector-container" @click="showTypePicker">
							<view class="selector-display">
								<view class="selector-icon">{{getTypeIcon(form.type || '请选择')}}</view>
								<text class="selector-text">{{form.type || '请选择房型类型'}}</text>
								<text class="selector-arrow">▼</text>
							</view>
						</view>
					</view>

					<view class="form-group modern-group">
						<view class="group-header">
							<text class="group-icon">🧭</text>
							<text class="group-label">床位朝向</text>
						</view>
						<view class="selector-container" @click="showDirectionPicker">
							<view class="selector-display">
								<view class="selector-icon">{{getDirectionIcon(form.direction || '请选择')}}</view>
								<text class="selector-text">{{form.direction || '请选择床位朝向'}}</text>
								<text class="selector-arrow">▼</text>
							</view>
						</view>
					</view>

					<view class="form-group modern-group">
						<view class="group-header">
							<text class="group-icon">🛗</text>
							<text class="group-label">电梯</text>
						</view>
						<view class="option-grid binary-grid">
							<view
								class="option-item modern-option binary-option"
								:class="{ 'active': form.isLift === item.name }"
								v-for="(item, index) in liftList"
								:key="index"
								@click="form.isLift = item.name"
							>
								<view class="option-icon">{{item.name === '是' ? '✅' : '❌'}}</view>
								<text class="option-text">{{item.name}}</text>
								<view class="option-check" v-if="form.isLift === item.name">✓</view>
							</view>
						</view>
					</view>

					<view class="form-group modern-group">
						<view class="group-header">
							<text class="group-icon">🚗</text>
							<text class="group-label">停车位</text>
						</view>
						<view class="option-grid binary-grid">
							<view
								class="option-item modern-option binary-option"
								:class="{ 'active': form.isParking === item.name }"
								v-for="(item, index) in parkingList"
								:key="index"
								@click="form.isParking = item.name"
							>
								<view class="option-icon">{{item.name === '是' ? '✅' : '❌'}}</view>
								<text class="option-text">{{item.name}}</text>
								<view class="option-check" v-if="form.isParking === item.name">✓</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 配置信息卡片 -->
			<view class="form-card modern-card">
				<view class="card-header gradient-header config-header">
					<view class="header-icon">🛋️</view>
					<view class="header-text">
						<text class="card-title">房屋配置</text>
						<text class="card-subtitle">选择房源提供的设施配置</text>
					</view>
				</view>
				<view class="card-content">
					<view class="config-grid">
						<view
							class="config-item modern-config"
							:class="{ 'checked': item.checked }"
							v-for="(item, index) in configList"
							:key="index"
							@click="toggleConfig(index)"
						>
							<view class="config-icon-wrapper">
								<text class="config-icon">{{getConfigIcon(item.name)}}</text>
								<view class="config-check" v-if="item.checked">
									<text class="check-icon">✓</text>
								</view>
							</view>
							<text class="config-text">{{item.name}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 周边设施卡片 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">🌍 周边设施</text>
				</view>
				<view class="card-content">
					<view class="checkbox-grid">
						<view
							class="checkbox-item"
							:class="{ 'checked': item.checked }"
							v-for="(item, index) in envList"
							:key="index"
							@click="toggleEnv(index)"
						>
							<text class="checkbox-icon">{{item.checked ? '✓' : ''}}</text>
							<text class="checkbox-text">{{item.name}}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 房源详情卡片 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">📝 房源详情</text>
				</view>
				<view class="card-content">
					<view class="input-group">
						<text class="input-label">房源标题</text>
						<input
							class="custom-input"
							v-model="form.title"
							placeholder="请输入房源标题"
							maxlength="50"
						/>
					</view>

					<view class="input-group">
						<text class="input-label">房源简介</text>
						<textarea
							class="custom-textarea"
							v-model="form.info"
							placeholder="请详细描述房源特色、周边环境等信息"
							maxlength="500"
						></textarea>
					</view>

					<view class="input-row">
						<view class="input-group half">
							<text class="input-label">租金(元/月)</text>
							<input
								class="custom-input"
								v-model="form.rent"
								type="number"
								placeholder="0"
							/>
						</view>
						<view class="input-group half">
							<text class="input-label">面积(m²)</text>
							<input
								class="custom-input"
								v-model="form.area"
								type="number"
								placeholder="0"
							/>
						</view>
					</view>

					<view class="input-group">
						<text class="input-label">楼层</text>
						<input
							class="custom-input"
							v-model="form.floor"
							placeholder="如：3/6层"
						/>
					</view>
				</view>
			</view>

			<!-- 联系信息卡片 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">📞 联系信息</text>
				</view>
				<view class="card-content">
					<view class="input-group">
						<text class="input-label">联系人</text>
						<input
							class="custom-input"
							v-model="form.name"
							placeholder="请输入联系人姓名"
						/>
					</view>

					<view class="input-group">
						<text class="input-label">联系方式</text>
						<view class="phone-input-container">
							<input
								class="custom-input phone-input"
								v-model="form.mobile"
								type="number"
								placeholder="请输入手机号"
							/>
							<button
								v-if="userInfo.mobile=='未绑定'||userInfo.mobile==null||userInfo.mobile==''"
								class="quick-input-btn"
								open-type="getPhoneNumber"
								@getphonenumber="getPhoneNumber"
							>
								一键输入
							</button>
						</view>
					</view>

					<view class="input-group">
						<text class="input-label">微信号</text>
						<input
							class="custom-input"
							v-model="form.weixin"
							placeholder="请输入微信号（选填）"
						/>
					</view>

					<view class="input-group">
						<text class="input-label">房源地址</text>

						<!-- 地图选择功能 -->
						<view class="address-selector" @click="openMapSelector">
							<view class="address-content">
								<text class="address-text" :class="{ 'placeholder': !form.address }">
									{{form.address || '点击选择地址'}}
								</text>
								<text v-if="form.latitude && form.longitude" class="address-coordinates">
									经纬度: {{form.latitude.toFixed(6)}}, {{form.longitude.toFixed(6)}}
								</text>
							</view>
							<text class="address-icon">📍</text>
						</view>

						<!-- 手动输入选项 -->
						<view class="manual-input-option">
							<text class="manual-link" @click="toggleManualInput">
								{{ showManualInput ? '使用地图选择' : '手动输入地址' }}
							</text>
						</view>

						<!-- 手动输入框 -->
						<view v-if="showManualInput" class="manual-address-input">
							<input
								class="custom-input"
								v-model="form.address"
								placeholder="请输入详细地址（如：北京市朝阳区xxx小区x号楼）"
								@input="clearCoordinates"
							/>
						</view>

						<view class="address-tip">
							<text class="tip-text">💡 建议使用地图选择获得精确位置，或手动输入详细地址</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 房源图片卡片 -->
			<view class="form-card">
				<view class="card-header">
					<text class="card-title">📷 房源图片</text>
					<text class="card-subtitle">最多上传9张图片</text>
				</view>
				<view class="card-content">
					<view class="image-upload-container">
						<view v-if="form.pics.length === 0" class="upload-placeholder" @click="upImage">
							<text class="upload-icon">📷</text>
							<text class="upload-text">点击上传图片</text>
							<text class="upload-hint">支持多张图片上传</text>
						</view>
						<view v-else class="image-grid">
							<view class="image-item add-image" @click="upImage">
								<text class="add-icon">+</text>
							</view>
							<view
								class="image-item"
								v-for="(item, index) in form.pics"
								:key="index"
							>
								<image class="uploaded-image" :src="item" mode="aspectFill"></image>
								<view class="image-delete" @click="removeImage(index)">×</view>
							</view>
						</view>
						<view v-if="form.pics.length > 0" class="clear-all-btn" @click="reset">
							清空所有图片
						</view>
					</view>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-container">
				<button class="submit-btn" @click="submit">
					<text class="submit-text">发布房源</text>
				</button>
			</view>
		</view>
	</view>

	<!-- 房型类型选择器弹窗 -->
	<view class="picker-overlay" v-if="showTypeModal" @click="hideTypePicker">
		<view class="picker-modal" @click.stop>
			<view class="picker-header">
				<view class="picker-title">
					<text class="picker-icon">🏠</text>
					<text class="picker-title-text">选择房型类型</text>
				</view>
				<view class="picker-close" @click="hideTypePicker">✕</view>
			</view>
			<view class="picker-content">
				<view class="picker-grid">
					<view
						class="picker-item"
						:class="{ 'active': tempType === item.name }"
						v-for="(item, index) in typeList"
						:key="index"
						@click="selectType(item.name)"
					>
						<view class="picker-item-icon">{{getTypeIcon(item.name)}}</view>
						<text class="picker-item-text">{{item.name}}</text>
						<view class="picker-item-check" v-if="tempType === item.name">✓</view>
					</view>
				</view>
			</view>
			<view class="picker-footer">
				<button class="picker-btn cancel" @click="hideTypePicker">取消</button>
				<button class="picker-btn confirm" @click="confirmType">确定</button>
			</view>
		</view>
	</view>

	<!-- 床位朝向选择器弹窗 -->
	<view class="picker-overlay" v-if="showDirectionModal" @click="hideDirectionPicker">
		<view class="picker-modal" @click.stop>
			<view class="picker-header">
				<view class="picker-title">
					<text class="picker-icon">🧭</text>
					<text class="picker-title-text">选择床位朝向</text>
				</view>
				<view class="picker-close" @click="hideDirectionPicker">✕</view>
			</view>
			<view class="picker-content">
				<view class="picker-grid">
					<view
						class="picker-item"
						:class="{ 'active': form.direction === item.name }"
						v-for="(item, index) in dirList"
						:key="index"
						@click="selectDirection(item.name)"
					>
						<view class="picker-item-icon">{{getDirectionIcon(item.name)}}</view>
						<text class="picker-item-text">{{item.name}}</text>
						<view class="picker-item-check" v-if="form.direction === item.name">✓</view>
					</view>
				</view>
			</view>
			<view class="picker-footer">
				<button class="picker-btn cancel" @click="hideDirectionPicker">取消</button>
				<button class="picker-btn confirm" @click="confirmDirection">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
	var vk = uni.vk;
	export default {
		data() {
			// 页面数据变量
			return {
				showManualInput: false, // 是否显示手动输入
				showTypeModal: false, // 是否显示房型选择弹窗
				tempType: '', // 临时选择的房型
				showDirectionModal: false, // 是否显示朝向选择弹窗
				tempDirection: '', // 临时选择的朝向
				configList: [
					{
						name: '空调',
						checked: false,
						disabled: false
					},
					{
						name: '洗衣机',
						checked: false,
						disabled: false
					},
					{
						name: '热水器',
						checked: false,
						disabled: false
					},
					{
						name: '冰箱',
						checked: false,
						disabled: false
					},
					{
						name: 'WiFi',
						checked: false,
						disabled: false
					},
					{
						name: '床',
						checked: false,
						disabled: false
					},
					{
						name: '衣柜',
						checked: false,
						disabled: false
					},
					{
						name: '沙发',
						checked: false,
						disabled: false
					},
					{
						name: '燃气灶',
						checked: false,
						disabled: false
					}
				],
				envList:[
					{
						name: '地铁',
						checked: false,
						disabled: false
					},
					{
						name: '公交站',
						checked: false,
						disabled: false
					},
					{
						name: '超市',
						checked: false,
						disabled: false
					},
					{
						name: '医院',
						checked: false,
						disabled: false
					},
					{
						name: '学校',
						checked: false,
						disabled: false
					}
				],
				typeList:[
					{
						name: '整租一居',
						disabled: false
					},
					{
						name: '整租两居',
						disabled: false
					},
					{
						name: '合租单间',
						disabled: false
					}
				],
				dirList:[
					{
						name: '东',
						checked: false,
						disabled: false
					},
					{
						name: '南',
						checked: false,
						disabled: false
					},
					{
						name: '西',
						checked: false,
						disabled: false
					},
					{
						name: '北',
						checked: false,
						disabled: false
					},
					{
						name: '南北通透',
						checked: false,
						disabled: false
					}
				],
				liftList:[
					{
						name: '是',
						disabled: false
					},
					{
						name: '否',
						disabled: false
					},
				],
				parkingList:[
					{
						name: '是',
						disabled: false
					},
					{
						name: '否',
						disabled: false
					},
				],
				image: "/static/empty.png",
				minDate: "",
				maxDate: "",
				form: {
					type: "",
					direction:"",
					isLift:"",
					isParking:"",
					title:"",
					rent:0,
					area:"",
					name: "",
					address:"",
					latitude: null,
					longitude: null,
					mobile: "",
					weixin: "",
					info: "",
					pics: []
				},
				userInfo: {},
				show: false,
				showManualInput: false
			}
		},
		onPageScroll(e) {
			
		},
		// 监听 - 页面每次【加载时】执行(如：前进)
		onLoad(options = {}) {
			vk = uni.vk;
			this.options = options;
			this.init(options);
		},
		onReachBottom() {
			
		},
		// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
		onReady() {},
		// 监听 - 页面每次【显示时】执行（如：前进和返回）（页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面）
		onShow() {
			// 页面显示时检查登录状态
			if (vk.checkToken()) {
				// 更新用户信息
				this.userInfo = vk.getVuex('$user.userInfo');
				console.log("页面显示，用户已登录：", this.userInfo);
			} else {
				console.log("页面显示，用户未登录");
			}
		},
		// 监听 - 页面每次【隐藏时】执行（如：返回）
		onHide() {},
		// 监听 - 页面每次【卸载时】（一般用于取消页面上的监听器）
		onUnload() {},
		// 监听 - 页面下拉刷新
		onPullDownRefresh() {
			setTimeout(() => {
				this.form = {
					type: "",
					direction:"",
					isLift:"",
					isParking:"",
					title:"",
					rent:0,
					area:"",
					name: "",
					address:"",
					latitude: null,
					longitude: null,
					mobile: "",
					weixin: "",
					info: "",
					pics: []
				}
				this.resetSelections(); // 重置选择状态
				uni.stopPullDownRefresh();
			}, 1000);
		},
		/**
		 * 监听 - 点击右上角转发时 文档 https://uniapp.dcloud.io/api/plugins/share?id=onshareappmessage
		 * 如果删除onShareAppMessage函数，则微信小程序右上角转发按钮会自动变灰
		 */
		onShareAppMessage(options) {

		},
		// 函数
		methods: {
			// 获取房型图标
			getTypeIcon(type) {
				const icons = {
					'整租一居': '🏠',
					'整租两居': '🏡',
					'合租单间': '🚪'
				};
				return icons[type] || '🏠';
			},

			// 获取朝向图标
			getDirectionIcon(direction) {
				const icons = {
					'东': '🌅',
					'南': '☀️',
					'西': '🌇',
					'北': '❄️',
					'南北通透': '🌬️'
				};
				return icons[direction] || '🧭';
			},

			// 获取配置图标
			getConfigIcon(config) {
				const icons = {
					'空调': '❄️',
					'洗衣机': '🧺',
					'热水器': '🚿',
					'冰箱': '🧊',
					'WiFi': '📶',
					'床': '🛏️',
					'衣柜': '👔',
					'沙发': '🛋️',
					'燃气灶': '🔥'
				};
				return icons[config] || '🏠';
			},

			// 显示房型选择弹窗
			showTypePicker() {
				this.tempType = this.form.type; // 保存当前选择
				this.showTypeModal = true;
			},

			// 隐藏房型选择弹窗
			hideTypePicker() {
				this.showTypeModal = false;
				this.tempType = '';
			},

			// 选择房型
			selectType(type) {
				this.tempType = type;
			},

			// 确认房型选择
			confirmType() {
				if (this.tempType) {
					this.form.type = this.tempType;
				}
				this.hideTypePicker();
			},

			// 显示朝向选择弹窗
			showDirectionPicker() {
				this.tempDirection = this.form.direction; // 保存当前选择
				this.showDirectionModal = true;
			},

			// 隐藏朝向选择弹窗
			hideDirectionPicker() {
				this.showDirectionModal = false;
				this.tempDirection = '';
			},

			// 选择朝向
			selectDirection(direction) {
				this.tempDirection = direction;
			},

			// 确认朝向选择
			confirmDirection() {
				if (this.tempDirection) {
					this.form.direction = this.tempDirection;
				}
				this.hideDirectionPicker();
			},

			// 页面数据初始化函数
			init(options = {}) {
				console.log("发布页面初始化");
				this.checkLoginStatus();
			},
			checkboxChange(e) {
				console.log(e);
			},
			radioChange1(e) {
				this.form.type = e
			},
			radioChange2(e) {
				this.form.direction = e
			},
			radioChange3(e) {
				this.form.isLift = e
			},
			radioChange4(e) {
				this.form.isParking = e
			},
			reset() {
				this.form.pics = []
			},
			removeImage(index) {
				this.form.pics.splice(index, 1)
			},
			clearCoordinates() {
				// 手动输入地址时清除坐标信息
				this.form.latitude = null;
				this.form.longitude = null;
			},

			// 打开地图选择器
			openMapSelector() {
				uni.navigateTo({
					url: `/pages/map/select/index?latitude=${this.form.latitude || ''}&longitude=${this.form.longitude || ''}`,
					events: {
						// 监听地址选择事件
						selectAddress: (data) => {
							console.log('选择的地址：', data);
							this.form.address = data.address;
							this.form.latitude = data.latitude;
							this.form.longitude = data.longitude;

							// 显示成功提示
							uni.showToast({
								title: '地址选择成功',
								icon: 'success'
							});
						}
					}
				});
			},

			// 切换手动输入模式
			toggleManualInput() {
				this.showManualInput = !this.showManualInput;
				if (this.showManualInput) {
					// 切换到手动输入时，清除坐标信息
					this.clearCoordinates();
				}
			},
			toggleConfig(index) {
				// 切换房屋配置选择状态
				console.log('点击配置项：', index, this.configList[index].name);
				this.$set(this.configList, index, {
					...this.configList[index],
					checked: !this.configList[index].checked
				});
				console.log('配置选择状态：', this.configList[index].name, this.configList[index].checked);
				// 强制更新视图
				this.$forceUpdate();
			},
			toggleEnv(index) {
				// 切换周边设施选择状态
				console.log('点击设施项：', index, this.envList[index].name);
				this.$set(this.envList, index, {
					...this.envList[index],
					checked: !this.envList[index].checked
				});
				console.log('设施选择状态：', this.envList[index].name, this.envList[index].checked);
				// 强制更新视图
				this.$forceUpdate();
			},
			resetSelections() {
				// 重置所有选择状态
				this.configList.forEach((item, index) => {
					this.$set(this.configList, index, {
						...item,
						checked: false
					});
				});
				this.envList.forEach((item, index) => {
					this.$set(this.envList, index, {
						...item,
						checked: false
					});
				});
			},
			checkPrivacyAgreement(callback) {
				// 检查隐私协议同意状态
				try {
					// 微信小程序隐私协议检查
					if (typeof wx !== 'undefined' && wx.requirePrivacyAuthorize) {
						wx.requirePrivacyAuthorize({
							success: () => {
								console.log('隐私协议已同意');
								callback && callback();
							},
							fail: (err) => {
								console.error('隐私协议检查失败：', err);
								uni.showModal({
									title: '隐私协议提示',
									content: '使用地图选择功能需要您同意隐私协议。请在弹出的隐私协议中点击"同意"后重试。',
									showCancel: true,
									cancelText: '取消',
									confirmText: '重试',
									success: (res) => {
										if (res.confirm) {
											// 用户点击重试，再次尝试
											this.checkPrivacyAgreement(callback);
										}
									}
								});
							}
						});
					} else {
						// 非微信环境或旧版本，直接执行回调
						callback && callback();
					}
				} catch (error) {
					console.error('隐私协议检查异常：', error);
					// 出现异常时显示手动输入选项
					uni.showModal({
						title: '提示',
						content: '无法使用地图选择功能，请使用手动输入地址。',
						showCancel: false,
						confirmText: '确定',
						success: () => {
							this.showManualInput = true;
						}
					});
				}
			},
			upImage() {
				let that = this
				uni.chooseImage({
					count: 9,
					sizeType: ['compressed'],
					success: (res) => {
						res.tempFilePaths.forEach(item => {
							vk.uploadFile({
								filePath: item,
								fileType: "image"
							}).then(res => {
								that.form.pics.push(res.fileURL)
							})
						})
					},
					complete: function() {}
				});
			},
			submit() {
				let that = this
				var jsonData = that.form
				jsonData.configList = ''
				jsonData.envList = ''
				this.configList.forEach(item=>{
					if(item.checked){
						jsonData.configList+=item.name+' '
					}
				})
				this.envList.forEach(item=>{
					if(item.checked){
						jsonData.envList+=item.name+' '
					}
				})				
				if (!vk.pubfn.test(jsonData.mobile, 'mobile')) {
					vk.toast('请输入正确的手机号')
					return
				}

				// 检查登录状态
				if (!vk.checkToken()) {
					vk.toast('请先登录');
					that.login();
					return;
				}

				let userInfo = vk.getVuex('$user.userInfo');
				if (!userInfo || !userInfo._id) {
					vk.toast('获取用户信息失败，请重新登录');
					that.login();
					return;
				}

				jsonData.user_id = userInfo._id;
				jsonData.status = "0"  // 默认状态为未审核
				jsonData.sort = 0
				console.log(jsonData)
				vk.callFunction({
					url: 'client/mall/goods/pub/add',
					data: jsonData
				}).then(res => {
					if (res.code == 0) {
						vk.toast('提交成功');
						vk.hideLoading();
						that.form = {
							type: "",
							direction:"",
							isLift:"",
							isParking:"",
							title:"",
							rent:0,
							area:"",
							name: "",
							address:"",
							latitude: null,
							longitude: null,
							mobile: "",
							weixin: "",
							info: "",
							pics: []
						}
						that.resetSelections(); // 重置选择状态
					}
				})
			},
			// 检查登录状态
			checkLoginStatus() {
				let that = this;
				console.log("检查登录状态");

				// 检查是否已登录
				if (vk.checkToken()) {
					// 已登录，获取用户信息
					console.log("用户已登录，获取用户信息");
					that.userInfo = vk.getVuex('$user.userInfo');
					console.log("当前用户信息：", that.userInfo);

					// 如果用户信息中有手机号，自动填入表单
					if (vk.pubfn.isNotNull(that.userInfo.mobile)) {
						that.form.mobile = that.userInfo.mobile;
					}
				} else {
					// 未登录，需要登录
					console.log("用户未登录，跳转登录");
					that.login();
				}
			},
			login() {
				let that = this;
				console.log("开始微信登录");

				wx.requirePrivacyAuthorize({
					success: () => {
						// 用户同意授权
						console.log("隐私授权成功，开始微信登录");
						vk.userCenter.loginByWeixin({
							data: {},
							success: (data) => {
								console.log("微信登录成功：", data);
								that.userInfo = data.userInfo;

								// 自动填入手机号
								if (vk.pubfn.isNotNull(data.userInfo.mobile)) {
									that.form.mobile = data.userInfo.mobile;
								}

								vk.toast("登录成功");
							},
							fail: (err) => {
								console.error("微信登录失败：", err);
								vk.toast("登录失败，请重试");
							}
						});
					},
					fail: () => {
						console.log("用户拒绝隐私授权");
						vk.toast("需要授权才能使用发布功能");
					},
					complete: () => {}
				});
			},
			chooseAddress() {
				// 先检查隐私协议同意状态
				this.checkPrivacyAgreement(() => {
					// 显示加载提示
					uni.showLoading({
						title: '打开地图中...',
						mask: true
					});

					// 调用地图选择位置API
					uni.chooseLocation({
					success: (res) => {
						console.log('选择的位置信息：', res);

						// 保存详细的地址信息
						this.form.address = res.address || res.name || '未知地址';
						this.form.latitude = res.latitude;
						this.form.longitude = res.longitude;

						// 如果有详细地址，优先使用详细地址
						if (res.address && res.name && res.address !== res.name) {
							this.form.address = `${res.name} (${res.address})`;
						}

						uni.hideLoading();

						// 显示成功提示
						uni.showToast({
							title: '地址选择成功',
							icon: 'success',
							duration: 1500
						});
					},
					fail: (err) => {
						console.error('选择地址失败：', err);
						uni.hideLoading();

						// 根据不同的错误类型显示不同的提示
						let errorMsg = '选择地址失败';
						if (err.errMsg) {
							if (err.errMsg.includes('cancel')) {
								errorMsg = '已取消选择地址';
							} else if (err.errMsg.includes('auth') || err.errMsg.includes('permission')) {
								errorMsg = '请授权位置信息后重试';
							} else if (err.errMsg.includes('privacy') || err.errMsg.includes('scope')) {
								errorMsg = '请在小程序设置中同意位置权限使用';
								// 显示更详细的提示
								uni.showModal({
									title: '位置权限提示',
									content: '使用地图选择功能需要位置权限。请在小程序右上角"..."菜单中找到"设置"，开启位置信息权限。',
									showCancel: false,
									confirmText: '我知道了'
								});
								return;
							} else if (err.errMsg.includes('system')) {
								errorMsg = '系统错误，请稍后重试';
							}
						}

						uni.showToast({
							title: errorMsg,
							icon: 'none',
							duration: 2000
						});
					},
					complete: () => {
						uni.hideLoading();
					}
				});
				});
			},
			getPhoneNumber(e) {
				let that = this;
				// 微信新增了code参数，可以直接传code，不再需要传 encryptedData 和 iv
				let {
					code
				} = e.detail;
				if (!code) {
					return false;
				}
				vk.userCenter.getPhoneNumber({
					data: {
						code,
						encryptedKey: that.encryptedKey
					},
					success: (data) => {
						that.form.mobile = data.mobile
						that.userInfo.mobile = data.mobile
						vk.callFunction({
							url: 'client/user/kh/update',
							data: {
								_id: that.userInfo._id,
								mobile: data.mobile
							},
							success: (res) => {
								
							}
						});
					}
				});
			},
			pageTo(path) {
				vk.navigateTo(path);
			},
			/**
			 * 检查JSON对象中必填字段是否为空
			 * @param {Object} data - 要检查的JSON数据
			 * @param {Array<string>} requiredFields - 必填字段数组
			 * @returns {Array<string>} - 为空的字段名称数组，若为空则表示所有必填字段都有值
			 */
			checkRequiredFields(data, requiredFields) {
			  const emptyFields = [];
			  
			  requiredFields.forEach(field => {
			    const value = data[field];
			    
			    // 判断值是否为空（空字符串、null、undefined）
			    if (value === '' || value === null || value === undefined) {
			      emptyFields.push(field);
			    }
			    
			    // 可选：检查空数组或空对象
			    if (Array.isArray(value) && value.length === 0) {
			      emptyFields.push(field);
			    }
			    
			    if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {
			      emptyFields.push(field);
			    }
			  });
			  
			  return emptyFields;
			}
		},
		// 监听器
		watch: {

		},
		// 计算属性
		computed: {

		}
	}
</script>

<style lang="scss">
page {
	background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
}

.publish-container {
	min-height: 100vh;
	padding-bottom: 40rpx;
}

/* 页面头部样式 */
.page-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx 40rpx;
	margin-bottom: 20rpx;

	.header-content {
		text-align: center;

		.header-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #ffffff;
			margin-bottom: 12rpx;
		}

		.header-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			line-height: 1.4;
		}
	}
}

/* 表单容器 */
.form-container {
	padding: 0 24rpx;
}

/* 卡片样式 */
.form-card {
	background: #ffffff;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;

	.card-header {
		background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
		padding: 24rpx 32rpx;
		border-bottom: 1px solid #e2e8f0;

		.card-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #1e293b;
		}

		.card-subtitle {
			font-size: 24rpx;
			color: #64748b;
			margin-left: 16rpx;
		}
	}

	.card-content {
		padding: 32rpx;
	}
}

/* 表单组样式 */
.form-group {
	margin-bottom: 32rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.group-label {
		display: block;
		font-size: 28rpx;
		font-weight: 600;
		color: #374151;
		margin-bottom: 16rpx;
	}
}

/* 单选框网格 */
.radio-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;

	.radio-item {
		flex: 1;
		min-width: 120rpx;
		padding: 16rpx 24rpx;
		background: #f8fafc;
		border: 2rpx solid #e2e8f0;
		border-radius: 16rpx;
		text-align: center;
		transition: all 0.3s ease;
		cursor: pointer;

		&:active {
			transform: scale(0.98);
		}

		&.active {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border-color: #667eea;
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

			.radio-text {
				color: #ffffff;
				font-weight: 600;
			}
		}

		.radio-text {
			font-size: 26rpx;
			font-weight: 500;
			color: #64748b;
			transition: color 0.3s ease;
		}
	}
}

/* 复选框网格 */
.checkbox-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;

	.checkbox-item {
		display: flex;
		align-items: center;
		padding: 16rpx 20rpx;
		background: #f8fafc;
		border: 2rpx solid #e2e8f0;
		border-radius: 16rpx;
		transition: all 0.3s ease;
		min-width: 120rpx;
		cursor: pointer;

		&:active {
			transform: scale(0.98);
		}

		&.checked {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border-color: #667eea;
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

			.checkbox-text {
				color: #ffffff;
				font-weight: 600;
			}

			.checkbox-icon {
				color: #ffffff;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
			}
		}

		.checkbox-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 12rpx;
			font-size: 20rpx;
			font-weight: bold;
			color: transparent;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
		}

		.checkbox-text {
			font-size: 26rpx;
			font-weight: 500;
			color: #64748b;
			transition: all 0.3s ease;
		}
	}
}

/* 输入框样式 */
.input-group {
	margin-bottom: 32rpx;

	&.half {
		flex: 1;
	}

	&:last-child {
		margin-bottom: 0;
	}

	.input-label {
		display: block;
		font-size: 28rpx;
		font-weight: 600;
		color: #374151;
		margin-bottom: 12rpx;
	}
}

.input-row {
	display: flex;
	gap: 20rpx;
}

.custom-input {
	width: 100%;
	padding: 20rpx 24rpx;
	background: #f8fafc;
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	font-size: 28rpx;
	color: #1e293b;
	transition: all 0.3s ease;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
	}
}

.custom-textarea {
	width: 100%;
	min-height: 160rpx;
	padding: 20rpx 24rpx;
	background: #f8fafc;
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	font-size: 28rpx;
	color: #1e293b;
	line-height: 1.5;
	resize: none;
	transition: all 0.3s ease;

	&:focus {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
	}
}

/* 手机号输入容器 */
.phone-input-container {
	display: flex;
	gap: 16rpx;
	align-items: center;

	.phone-input {
		flex: 1;
	}

	.quick-input-btn {
		padding: 20rpx 24rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		border: none;
		border-radius: 16rpx;
		font-size: 24rpx;
		font-weight: 500;
		white-space: nowrap;
	}
}

/* 地址选择器 */
.address-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 24rpx;
	background: #f8fafc;
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	transition: all 0.3s ease;
	min-height: 80rpx;

	&:active {
		background: #e2e8f0;
	}

	.address-content {
		flex: 1;

		.address-text {
			display: block;
			font-size: 28rpx;
			color: #1e293b;
			line-height: 1.4;
			margin-bottom: 4rpx;

			&.placeholder {
				color: #9ca3af;
			}
		}

		.address-coordinates {
			display: block;
			font-size: 22rpx;
			color: #64748b;
			opacity: 0.8;
		}
	}

	.address-icon {
		font-size: 32rpx;
		margin-left: 16rpx;
		color: #667eea;
	}
}

/* 手动输入提示 */
.manual-input-tip {
	margin-top: 12rpx;
	text-align: center;

	.tip-text {
		font-size: 24rpx;
		color: #667eea;
		text-decoration: underline;
	}
}

.manual-input-option {
	margin-top: 16rpx;
	text-align: center;

	.manual-link {
		font-size: 26rpx;
		color: #007AFF;
		text-decoration: underline;
		padding: 12rpx;

		&:active {
			opacity: 0.7;
		}
	}
}

.manual-address-input {
	margin-top: 16rpx;
}

/* 地址提示样式 */
.address-tip {
	margin-top: 12rpx;
	padding: 16rpx;
	background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
	border-radius: 12rpx;
	border-left: 4rpx solid #0ea5e9;

	.tip-text {
		font-size: 24rpx;
		color: #0369a1;
		line-height: 1.4;
	}
}

/* 图片上传样式 */
.image-upload-container {
	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;
		background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
		border: 3rpx dashed #cbd5e1;
		border-radius: 20rpx;
		transition: all 0.3s ease;

		&:active {
			background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
		}

		.upload-icon {
			font-size: 80rpx;
			margin-bottom: 16rpx;
		}

		.upload-text {
			font-size: 32rpx;
			font-weight: 600;
			color: #475569;
			margin-bottom: 8rpx;
		}

		.upload-hint {
			font-size: 24rpx;
			color: #64748b;
		}
	}

	.image-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;

		.image-item {
			position: relative;
			width: 200rpx;
			height: 200rpx;
			border-radius: 16rpx;
			overflow: hidden;

			&.add-image {
				display: flex;
				align-items: center;
				justify-content: center;
				background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
				border: 3rpx dashed #cbd5e1;

				.add-icon {
					font-size: 60rpx;
					color: #64748b;
				}
			}

			.uploaded-image {
				width: 100%;
				height: 100%;
			}

			.image-delete {
				position: absolute;
				top: 8rpx;
				right: 8rpx;
				width: 40rpx;
				height: 40rpx;
				background: rgba(239, 68, 68, 0.9);
				color: #ffffff;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				font-weight: bold;
			}
		}
	}

	.clear-all-btn {
		margin-top: 24rpx;
		padding: 16rpx 32rpx;
		background: #fee2e2;
		color: #dc2626;
		border-radius: 12rpx;
		text-align: center;
		font-size: 26rpx;
		font-weight: 500;
		transition: all 0.3s ease;

		&:active {
			background: #fecaca;
		}
	}
}

/* 提交按钮 */
.submit-container {
	padding: 40rpx 24rpx;

	.submit-btn {
		width: 100%;
		padding: 24rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: none;
		border-radius: 20rpx;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		}

		.submit-text {
			font-size: 32rpx;
			font-weight: 600;
			color: #ffffff;
		}
	}
}

/* ========== 现代化设计样式 ========== */

/* 现代化卡片增强 */
.modern-card {
	border: 1rpx solid #f1f5f9;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
}

/* 渐变头部 */
.gradient-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx !important;

	.header-icon {
		font-size: 48rpx;
		margin-right: 20rpx;
	}

	.header-text {
		flex: 1;

		.card-title {
			font-size: 32rpx;
			font-weight: 600;
			color: white !important;
			margin-bottom: 8rpx;
		}

		.card-subtitle {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.8) !important;
			margin-left: 0 !important;
		}
	}
}

.config-header {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 现代化表单组 */
.modern-group {
	margin-bottom: 40rpx;

	.group-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.group-icon {
			font-size: 32rpx;
			margin-right: 12rpx;
		}

		.group-label {
			font-size: 28rpx;
			font-weight: 600;
			color: #374151;
			margin-bottom: 0 !important;
		}
	}
}

/* 现代化选项网格 */
.option-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.binary-grid {
	gap: 20rpx;
}

/* 现代化选项项 */
.modern-option {
	position: relative;
	flex: 1;
	min-width: 140rpx;
	padding: 20rpx 16rpx;
	background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	text-align: center;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

	.option-icon {
		font-size: 32rpx;
		margin-bottom: 8rpx;
		display: block;
	}

	.option-text {
		font-size: 26rpx;
		color: #475569;
		font-weight: 500;
	}

	.option-check {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		width: 32rpx;
		height: 32rpx;
		background: linear-gradient(135deg, #10b981 0%, #059669 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20rpx;
		color: white;
		font-weight: bold;
		box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
	}

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
	}

	&.active {
		background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
		border-color: #3b82f6;
		color: #1e40af;
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.2);

		.option-text {
			color: #1e40af;
			font-weight: 600;
		}
	}
}

.binary-option {
	flex: 0 0 calc(50% - 10rpx);
	min-width: auto;
}

/* 现代化配置网格 */
.config-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

/* 现代化配置项 */
.modern-config {
	position: relative;
	flex: 0 0 calc(25% - 12rpx);
	min-width: 120rpx;
	padding: 24rpx 16rpx;
	background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 20rpx;
	text-align: center;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

	.config-icon-wrapper {
		position: relative;
		margin-bottom: 12rpx;

		.config-icon {
			font-size: 40rpx;
			display: block;
		}

		.config-check {
			position: absolute;
			top: -8rpx;
			right: 50%;
			transform: translateX(50%);
			width: 28rpx;
			height: 28rpx;
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);

			.check-icon {
				font-size: 16rpx;
				color: white;
				font-weight: bold;
			}
		}
	}

	.config-text {
		font-size: 24rpx;
		color: #475569;
		font-weight: 500;
	}

	&:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.1);
	}

	&.checked {
		background: linear-gradient(145deg, #ecfdf5 0%, #d1fae5 100%);
		border-color: #10b981;
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 32rpx rgba(16, 185, 129, 0.15);

		.config-text {
			color: #047857;
			font-weight: 600;
		}
	}
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.modern-config {
		flex: 0 0 calc(33.333% - 11rpx);
	}
}

@media (max-width: 600rpx) {
	.modern-config {
		flex: 0 0 calc(50% - 8rpx);
	}
}

/* ========== 选择器样式 ========== */

/* 选择器显示区域 */
.selector-container {
	margin-top: 16rpx;
}

.selector-display {
	display: flex;
	align-items: center;
	padding: 20rpx 24rpx;
	background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	transition: all 0.3s ease;
	cursor: pointer;

	.selector-icon {
		font-size: 32rpx;
		margin-right: 16rpx;
	}

	.selector-text {
		flex: 1;
		font-size: 28rpx;
		color: #374151;
		font-weight: 500;
	}

	.selector-arrow {
		font-size: 24rpx;
		color: #9ca3af;
		transition: transform 0.3s ease;
	}

	&:hover {
		border-color: #3b82f6;
		box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);

		.selector-arrow {
			transform: rotate(180deg);
		}
	}
}

/* 弹窗遮罩 */
.picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}

/* 弹窗主体 */
.picker-modal {
	width: 600rpx;
	max-width: 90vw;
	background: white;
	border-radius: 24rpx;
	overflow: hidden;
	animation: slideUp 0.3s ease;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

@keyframes slideUp {
	from {
		opacity: 0;
		transform: translateY(100rpx) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* 弹窗头部 */
.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;

	.picker-title {
		display: flex;
		align-items: center;

		.picker-icon {
			font-size: 40rpx;
			margin-right: 16rpx;
		}

		.picker-title-text {
			font-size: 32rpx;
			font-weight: 600;
		}
	}

	.picker-close {
		width: 48rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.2);
		font-size: 28rpx;
		cursor: pointer;
		transition: all 0.3s ease;

		&:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: rotate(90deg);
		}
	}
}

/* 弹窗内容 */
.picker-content {
	padding: 32rpx;
}

.picker-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.picker-item {
	position: relative;
	flex: 0 0 calc(50% - 8rpx);
	padding: 24rpx 20rpx;
	background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	text-align: center;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;

	.picker-item-icon {
		font-size: 40rpx;
		margin-bottom: 12rpx;
		display: block;
	}

	.picker-item-text {
		font-size: 28rpx;
		color: #475569;
		font-weight: 500;
	}

	.picker-item-check {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		width: 32rpx;
		height: 32rpx;
		background: linear-gradient(135deg, #10b981 0%, #059669 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 20rpx;
		color: white;
		font-weight: bold;
		box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
	}

	&:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
	}

	&.active {
		background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
		border-color: #3b82f6;
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.2);

		.picker-item-text {
			color: #1e40af;
			font-weight: 600;
		}
	}
}

/* 弹窗底部 */
.picker-footer {
	display: flex;
	gap: 16rpx;
	padding: 24rpx 32rpx 32rpx;
	background: #f8fafc;
}

.picker-btn {
	flex: 1;
	padding: 20rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	transition: all 0.3s ease;
	cursor: pointer;

	&.cancel {
		background: #f1f5f9;
		color: #64748b;

		&:hover {
			background: #e2e8f0;
			color: #475569;
		}
	}

	&.confirm {
		background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
		color: white;

		&:hover {
			background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
			transform: translateY(-2rpx);
			box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
		}
	}
}
}
</style>