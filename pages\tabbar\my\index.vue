<template>
	<view class="page-container">
		<!-- 顶部用户信息区域 -->
		<view class="header-section">
			<view class="user-card" @click="handleUserCardClick">
				<view class="avatar-container">
					<u-avatar
						:src="userInfo.avatar ? userInfo.avatar : '/static/default.png'"
						size="120"
						:fade="true"
						duration="450"
						:lazy-load="true"
					></u-avatar>
					<view class="avatar-border"></view>
				</view>
				<view class="user-info">
					<view class="welcome-text">
						<text class="greeting">👋 欢迎您</text>
						<text class="username">{{ userInfo.nickname || '您还未登录' }}</text>
					</view>
					<view class="user-detail">
						<text v-if="!userInfo._id" class="complete-info">
							✨ 点击完善资料
						</text>
						<text v-else-if="userInfo.mobile == '未绑定' || userInfo.mobile == null || userInfo.mobile == ''" class="complete-info">
							✨ 点击完善资料
						</text>
						<text v-else class="phone-info">
							📱 {{ userInfo.mobile }}
						</text>
					</view>
				</view>
				<view class="arrow-icon">
					<u-icon name="arrow-right" color="#fff" size="28"></u-icon>
				</view>
			</view>
		</view>

		<!-- 主要功能区域 -->
		<view class="main-content">
			<!-- 房源管理 -->
			<view class="section-card">
				<view class="section-header">
					<view class="section-title">
						<text class="section-icon">🏠</text>
						<text class="section-name">房源管理</text>
					</view>
				</view>
				<view class="menu-grid">
					<view class="menu-item" @click="switchToPublish">
						<view class="menu-icon publish-icon">
							<u-icon name="plus-circle" color="#fff" size="32"></u-icon>
						</view>
						<text class="menu-title">发布房源</text>
						<text class="menu-desc">快速发布</text>
					</view>
					<view class="menu-item" @click="pageTo('/pages/my/publish/index')">
						<view class="menu-icon manage-icon">
							<u-icon name="home" color="#fff" size="32"></u-icon>
						</view>
						<text class="menu-title">我的发布</text>
						<text class="menu-desc">管理房源</text>
					</view>
					<view class="menu-item" @click="pageTo('/pages/my/order/index')">
						<view class="menu-icon order-icon">
							<u-icon name="order" color="#fff" size="32"></u-icon>
						</view>
						<text class="menu-title">预约订单</text>
						<text class="menu-desc">查看订单</text>
					</view>
					<view class="menu-item" @click="pageTo('/pages/my/fav/index')">
						<view class="menu-icon fav-icon">
							<u-icon name="bookmark" color="#fff" size="32"></u-icon>
						</view>
						<text class="menu-title">我的收藏</text>
						<text class="menu-desc">收藏列表</text>
					</view>
				</view>
			</view>

			<!-- 服务支持 -->
			<view class="section-card">
				<view class="section-header">
					<view class="section-title">
						<text class="section-icon">🛠️</text>
						<text class="section-name">服务支持</text>
					</view>
				</view>
				<view class="service-list">
					<view class="service-item" @click="pageTo('/pages/my/feedback/index')">
						<view class="service-icon feedback-icon">
							<u-icon name="edit-pen" color="#667eea" size="28"></u-icon>
						</view>
						<view class="service-content">
							<text class="service-title">投诉建议</text>
							<text class="service-desc">意见反馈</text>
						</view>
						<u-icon name="arrow-right" color="#c0c4cc" size="24"></u-icon>
					</view>
				</view>
			</view>

			<!-- 法律条款 -->
			<view class="section-card">
				<view class="section-header">
					<view class="section-title">
						<text class="section-icon">📋</text>
						<text class="section-name">法律条款</text>
					</view>
				</view>
				<view class="service-list">
					<view class="service-item" @click="pageTo('/pages/page/index?key=yszc')">
						<view class="service-icon privacy-icon">
							<u-icon name="file-text" color="#10b981" size="28"></u-icon>
						</view>
						<view class="service-content">
							<text class="service-title">用户隐私</text>
							<text class="service-desc">隐私政策</text>
						</view>
						<u-icon name="arrow-right" color="#c0c4cc" size="24"></u-icon>
					</view>
					<view class="service-item" @click="pageTo('/pages/page/index?key=yhxy')">
						<view class="service-icon agreement-icon">
							<u-icon name="file-text" color="#f59e0b" size="28"></u-icon>
						</view>
						<view class="service-content">
							<text class="service-title">用户协议</text>
							<text class="service-desc">服务条款</text>
						</view>
						<u-icon name="arrow-right" color="#c0c4cc" size="24"></u-icon>
					</view>
				</view>
			</view>

			<!-- 退出登录 -->
			<view class="logout-section">
				<view class="logout-btn" @click="loginOut">
					<u-icon name="info-circle-fill" color="#ef4444" size="28"></u-icon>
					<text class="logout-text">退出登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var vk = uni.vk;
	export default {
		data() {
			// 页面数据变量
			return {
				titleStyle: {
					paddingLeft:'20rpx'
				 },
				userInfo:{
					avatar:'/static/default.png',
					nickname:'游客',
					mobile:""
				},
				isLogin: false // 登录状态标识
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		// 监听 - 页面每次【加载时】执行(如：前进)
		onLoad(options = {}) {
			vk = uni.vk;
			this.options = options;
			this.init(options);
		},
		// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发
		onReady(){},
		// 监听 - 页面每次【显示时】执行（如：前进和返回）（页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面）
		onShow() {
			
		},
		// 监听 - 页面每次【隐藏时】执行（如：返回）
		onHide() {},
		// 监听 - 页面每次【卸载时】（一般用于取消页面上的监听器）
		onUnload(){},
		// 监听 - 页面下拉刷新
		onPullDownRefresh() {
				let that = this;
				setTimeout(() => {
					vk.userCenter.getCurrentUserInfo({
					  success: (res) => {
						 that.userInfo = vk.getVuex('$user.userInfo')
					  }
					});	
					uni.stopPullDownRefresh();
				}, 1000);
			},
		/**
		 * 监听 - 点击右上角转发时 文档 https://uniapp.dcloud.io/api/plugins/share?id=onshareappmessage
		 * 如果删除onShareAppMessage函数，则微信小程序右上角转发按钮会自动变灰
		 */
		onShareAppMessage(options) {
			
		},
		// 函数
		methods: {
			// 页面数据初始化函数
			init(options={}){
				console.log("我的页面初始化: ", options);
				let that = this;
				if (vk.checkToken()) {
				  // token有效
				  that.isLogin = true;
				  that.userInfo = vk.getVuex('$user.userInfo');
				  console.log("用户已登录，用户信息:", that.userInfo);
				} else {
					that.isLogin = false;
					console.log("用户未登录");
					that.login();
				}
			},
			login() {
				let that = this
				if(that.isLogin){
					wx.requirePrivacyAuthorize({
						 success: () => {
						   // 用户同意授权
						   // 继续小程序逻辑
						   vk.userCenter.loginByWeixin({
								data:{},
								success: (data) => {
									that.userInfo = data.userInfo
								} 
							   });
						 },
						 fail: () => {
							console.log("拒绝")
						 }, // 用户拒绝授权
						 complete: () => {}
					   })
				}else{
					vk.navigateTo('/pages/auth/index');
				}
			},
			loginOut() {
				this.userInfo = {
					avatar:'/static/default.png',
					nickname:'您还未登录',
					mobile:"未绑定"
				}
				vk.setVuex('$user.userInfo', {})
				vk.userCenter.logout()
				vk.toast('您已经成功退出!')
			},
			pageTo(path) {
				vk.navigateTo(path);
			},

			// 处理用户卡片点击
			handleUserCardClick() {
				const userInfo = vk.getVuex('$user.userInfo');
				if (!userInfo || !userInfo._id) {
					// 未登录，跳转到新的登录注册页面
					vk.navigateTo('/pages/auth/index');
				} else {
					// 已登录，跳转到用户信息编辑页面
					vk.navigateTo('/pages/userInfo/index');
				}
			},
			// 切换到发布房源页面（底部tabbar）
			switchToPublish() {
				console.log('=== 发布房源按钮被点击 ===');

				// 检查登录状态
				const isLoggedIn = vk.checkToken();
				console.log('登录状态检查:', isLoggedIn);

				if (!isLoggedIn) {
					console.log('用户未登录，跳转到登录页面');
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					// 跳转到登录页面
					vk.navigateTo('/pages/auth/index');
					return;
				}

				console.log('用户已登录，切换到发布房源页面');

				// 直接跳转到发布房源页面
				console.log('准备跳转到发布房源页面');

				// 使用 navigateTo 而不是 switchTab，因为发布页面可能不在 tabBar 中
				uni.navigateTo({
					url: '/pages/tabbar/add/index',
					success: () => {
						console.log('成功跳转到发布房源页面');
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						uni.showToast({
							title: '跳转失败，请重试',
							icon: 'none'
						});
					}
				});
			},
		},
		// 监听器
		watch:{
			
		},
		// 计算属性
		computed:{
			
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
}

/* 顶部用户信息区域 */
.header-section {
	padding: 60rpx 40rpx 40rpx;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
		backdrop-filter: blur(10rpx);
	}
}

.user-card {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(20rpx);
	border-radius: 24rpx;
	padding: 32rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.avatar-container {
	position: relative;
	margin-right: 24rpx;

	.avatar-border {
		position: absolute;
		top: -6rpx;
		left: -6rpx;
		right: -6rpx;
		bottom: -6rpx;
		border-radius: 50%;
		background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.5));
		z-index: -1;
	}
}

.user-info {
	flex: 1;

	.welcome-text {
		display: flex;
		flex-direction: column;
		margin-bottom: 12rpx;

		.greeting {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.8);
			margin-bottom: 8rpx;
		}

		.username {
			font-size: 36rpx;
			font-weight: 600;
			color: #fff;
		}
	}

	.user-detail {
		.complete-info {
			font-size: 26rpx;
			color: #fbbf24;
			font-weight: 500;
		}

		.phone-info {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}
}

.arrow-icon {
	opacity: 0.8;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	background: #f8f9fa;
	border-radius: 32rpx 32rpx 0 0;
	margin-top: -20rpx;
	padding: 40rpx 24rpx 120rpx;
	position: relative;
	z-index: 1;
}

/* 卡片样式 */
.section-card {
	background: #fff;
	border-radius: 20rpx;
	margin-bottom: 32rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
	padding: 32rpx 32rpx 24rpx;
	border-bottom: 1rpx solid #f1f3f4;

	.section-title {
		display: flex;
		align-items: center;

		.section-icon {
			font-size: 32rpx;
			margin-right: 16rpx;
		}

		.section-name {
			font-size: 32rpx;
			font-weight: 600;
			color: #1a1a1a;
		}
	}
}

/* 网格菜单 */
.menu-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 24rpx;
	padding: 32rpx;
}

.menu-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 32rpx 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	}
}

.menu-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;

	&.publish-icon {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	&.manage-icon {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	}

	&.order-icon {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	}

	&.fav-icon {
		background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
	}
}

.menu-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #1a1a1a;
	margin-bottom: 8rpx;
}

.menu-desc {
	font-size: 22rpx;
	color: #666;
}

/* 服务列表 */
.service-list {
	padding: 0 32rpx 16rpx;
}

.service-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f1f3f4;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background: #f8f9fa;
		margin: 0 -32rpx;
		padding-left: 32rpx;
		padding-right: 32rpx;
	}
}

.service-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;

	&.feedback-icon {
		background: rgba(102, 126, 234, 0.1);
	}

	&.privacy-icon {
		background: rgba(16, 185, 129, 0.1);
	}

	&.agreement-icon {
		background: rgba(245, 158, 11, 0.1);
	}
}

.service-content {
	flex: 1;

	.service-title {
		font-size: 30rpx;
		font-weight: 500;
		color: #1a1a1a;
		margin-bottom: 4rpx;
		display: block;
	}

	.service-desc {
		font-size: 24rpx;
		color: #666;
		display: block;
	}
}

/* 退出登录 */
.logout-section {
	margin-top: 40rpx;
}

.logout-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		background: #fef2f2;
	}

	.logout-text {
		font-size: 30rpx;
		font-weight: 500;
		color: #ef4444;
		margin-left: 16rpx;
	}
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.menu-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 16rpx;
		padding: 24rpx;
	}

	.menu-item {
		padding: 24rpx 16rpx;
	}

	.menu-icon {
		width: 64rpx;
		height: 64rpx;
	}
}
</style>
