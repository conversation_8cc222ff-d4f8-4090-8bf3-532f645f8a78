{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/web/wxproject/预约租房/pages/tabbar/add/index.vue?a5dd", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/add/index.vue?a068", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/add/index.vue?897d", "uni-app:///pages/tabbar/add/index.vue", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/add/index.vue?a330"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showManualInput", "showTypeModal", "tempType", "showDirectionModal", "tempDirection", "configList", "name", "checked", "disabled", "envList", "typeList", "dirList", "liftList", "parkingList", "image", "minDate", "maxDate", "form", "type", "direction", "isLift", "isParking", "title", "rent", "area", "address", "latitude", "longitude", "mobile", "weixin", "info", "pics", "userInfo", "show", "onPageScroll", "onLoad", "vk", "onReachBottom", "onReady", "onShow", "console", "onHide", "onUnload", "onPullDownRefresh", "setTimeout", "uni", "onShareAppMessage", "methods", "getTypeIcon", "getDirectionIcon", "getConfigIcon", "showTypePicker", "hideTypePicker", "selectType", "confirmType", "showDirectionPicker", "hideDirectionPicker", "selectDirection", "confirmDirection", "init", "checkboxChange", "radioChange1", "radioChange2", "radioChange3", "radioChange4", "reset", "removeImage", "clearCoordinates", "openMapSelector", "url", "events", "selectAddress", "icon", "toggleManualInput", "toggleConfig", "toggleEnv", "resetSelections", "item", "checkPrivacyAgreement", "success", "callback", "fail", "content", "showCancel", "cancelText", "confirmText", "upImage", "count", "sizeType", "res", "filePath", "fileType", "that", "complete", "submit", "jsonData", "checkLoginStatus", "login", "<PERSON><PERSON><PERSON><PERSON>", "mask", "duration", "errorMsg", "getPhoneNumber", "code", "e", "encrypted<PERSON>ey", "_id", "pageTo", "checkRequiredFields", "requiredFields", "emptyFields", "watch", "computed"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC4K;AAC5K,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuYnqB;AAAA,eACA;EACAC;IACA;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC,aACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAC,UACA;QACAH;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAE,WACA;QACAJ;QACAE;MACA,GACA;QACAF;QACAE;MACA,GACA;QACAF;QACAE;MACA,EACA;MACAG,UACA;QACAL;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAI,WACA;QACAN;QACAE;MACA,GACA;QACAF;QACAE;MACA,EACA;MACAK,cACA;QACAP;QACAE;MACA,GACA;QACAF;QACAE;MACA,EACA;MACAM;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAlB;QACAmB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IAAA,sBACA;EAEA;EACAC,wCAEA;EACA;EACAC;IAAA;IACAC;IACA;IACA;EACA;EACAC,yCAEA;EACA;EACAC;EACA;EACAC;IACA;IACA;MACA;MACA;MACAC;IACA;MACAA;IACA;EACA;EACA;EACAC;EACA;EACAC;EACA;EACAC;IAAA;IACAC;MACA;QACA1B;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAlB;QACAmB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAc;IACA;EACA;EACA;AACA;AACA;AACA;EACAC,wDAEA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAnB;MACA;IACA;IACAoB;MACApB;IACA;IACAqB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACAvB;QACAwB;QACAC;UACA;UACAC;YACA/B;YACA;YACA;YACA;;YAEA;YACAK;cACAvB;cACAkD;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACAlC;MACA,kEACA;QACAjC;MAAA,GACA;MACAiC;MACA;MACA;IACA;IACAmC;MACA;MACAnC;MACA,+DACA;QACAjC;MAAA,GACA;MACAiC;MACA;MACA;IACA;IACAoC;MAAA;MACA;MACA;QACA,sEACAC;UACAtE;QAAA,GACA;MACA;MACA;QACA,mEACAsE;UACAtE;QAAA,GACA;MACA;IACA;IACAuE;MAAA;MACA;MACA;QACA;QACA;UACApF;YACAqF;cACAvC;cACAwC;YACA;YACAC;cACAzC;cACAK;gBACAvB;gBACA4D;gBACAC;gBACAC;gBACAC;gBACAN;kBACA;oBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;UACA;UACAC;QACA;MACA;QACAxC;QACA;QACAK;UACAvB;UACA4D;UACAC;UACAE;UACAN;YACA;UACA;QACA;MACA;IACA;IACAO;MACA;MACAzC;QACA0C;QACAC;QACAT;UACAU;YACArD;cACAsD;cACAC;YACA;cACAC;YACA;UACA;QACA;QACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACA;QACA;UACAA;QACA;MACA;MACA;QACA;UACAA;QACA;MACA;MACA;QACA3D;QACA;MACA;;MAEA;MACA;QACAA;QACAwD;QACA;MACA;MAEA;MACA;QACAxD;QACAwD;QACA;MACA;MAEAG;MACAA;MACAA;MACAvD;MACAJ;QACAiC;QACAtE;MACA;QACA;UACAqC;UACAA;UACAwD;YACA1E;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAlB;YACAmB;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;UACA6D;QACA;MACA;IACA;IACA;IACAI;MACA;MACAxD;;MAEA;MACA;QACA;QACAA;QACAoD;QACApD;;QAEA;QACA;UACAoD;QACA;MACA;QACA;QACApD;QACAoD;MACA;IACA;IACAK;MACA;MACAzD;MAEA9C;QACAqF;UACA;UACAvC;UACAJ;YACArC;YACAgF;cACAvC;cACAoD;;cAEA;cACA;gBACAA;cACA;cAEAxD;YACA;YACA6C;cACAzC;cACAJ;YACA;UACA;QACA;QACA6C;UACAzC;UACAJ;QACA;QACAyD;MACA;IACA;IACAK;MAAA;MACA;MACA;QACA;QACArD;UACAvB;UACA6E;QACA;;QAEA;QACAtD;UACAkC;YACAvC;;YAEA;YACA;YACA;YACA;;YAEA;YACA;cACA;YACA;YAEAK;;YAEA;YACAA;cACAvB;cACAkD;cACA4B;YACA;UACA;UACAnB;YACAzC;YACAK;;YAEA;YACA;YACA;cACA;gBACAwD;cACA;gBACAA;cACA;gBACAA;gBACA;gBACAxD;kBACAvB;kBACA4D;kBACAC;kBACAE;gBACA;gBACA;cACA;gBACAgB;cACA;YACA;YAEAxD;cACAvB;cACAkD;cACA4B;YACA;UACA;UACAP;YACAhD;UACA;QACA;MACA;IACA;IACAyD;MACA;MACA;MACA,IACAC,OACAC,SADAD;MAEA;QACA;MACA;MACAnE;QACArC;UACAwG;UACAE;QACA;QACA1B;UACAa;UACAA;UACAxD;YACAiC;YACAtE;cACA2G;cACA9E;YACA;YACAmD,gCAEA;UACA;QACA;MACA;IACA;IACA4B;MACAvE;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAwE;MACA;MAEAC;QACA;;QAEA;QACA;UACAC;QACA;;QAEA;QACA;UACAA;QACA;QAEA;UACAA;QACA;MACA;MAEA;IACA;EACA;EACA;EACAC,QAEA;EACA;EACAC,WAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACjnCA;AAAA;AAAA;AAAA;AAA8tC,CAAgB,ynCAAG,EAAC,C", "file": "pages/tabbar/add/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/tabbar/add/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=68ff346c&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/add/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=68ff346c&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"publish-container\">\r\n\t\t<!-- 页面头部 -->\r\n\t\t<view class=\"page-header\">\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<text class=\"header-title\">🏠 发布房源</text>\r\n\t\t\t\t<text class=\"header-subtitle\">填写详细信息，让租客更容易找到您的房源</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"form-container\">\r\n\t\t\t<!-- 基本信息卡片 -->\r\n\t\t\t<view class=\"form-card modern-card\">\r\n\t\t\t\t<view class=\"card-header gradient-header\">\r\n\t\t\t\t\t<view class=\"header-icon\">🏘️</view>\r\n\t\t\t\t\t<view class=\"header-text\">\r\n\t\t\t\t\t\t<text class=\"card-title\">基本信息</text>\r\n\t\t\t\t\t\t<text class=\"card-subtitle\">选择房源的基本属性</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"form-group modern-group\">\r\n\t\t\t\t\t\t<view class=\"group-header\">\r\n\t\t\t\t\t\t\t<text class=\"group-icon\">🏠</text>\r\n\t\t\t\t\t\t\t<text class=\"group-label\">房型类型</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"selector-container\" @click=\"showTypePicker\">\r\n\t\t\t\t\t\t\t<view class=\"selector-display\">\r\n\t\t\t\t\t\t\t\t<view class=\"selector-icon\">{{getTypeIcon(form.type || '请选择')}}</view>\r\n\t\t\t\t\t\t\t\t<text class=\"selector-text\">{{form.type || '请选择房型类型'}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"selector-arrow\">▼</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"form-group modern-group\">\r\n\t\t\t\t\t\t<view class=\"group-header\">\r\n\t\t\t\t\t\t\t<text class=\"group-icon\">🧭</text>\r\n\t\t\t\t\t\t\t<text class=\"group-label\">床位朝向</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"selector-container\" @click=\"showDirectionPicker\">\r\n\t\t\t\t\t\t\t<view class=\"selector-display\">\r\n\t\t\t\t\t\t\t\t<view class=\"selector-icon\">{{getDirectionIcon(form.direction || '请选择')}}</view>\r\n\t\t\t\t\t\t\t\t<text class=\"selector-text\">{{form.direction || '请选择床位朝向'}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"selector-arrow\">▼</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"form-group modern-group\">\r\n\t\t\t\t\t\t<view class=\"group-header\">\r\n\t\t\t\t\t\t\t<text class=\"group-icon\">🛗</text>\r\n\t\t\t\t\t\t\t<text class=\"group-label\">电梯</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"option-grid binary-grid\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tclass=\"option-item modern-option binary-option\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'active': form.isLift === item.name }\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in liftList\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\t@click=\"form.isLift = item.name\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<view class=\"option-icon\">{{item.name === '是' ? '✅' : '❌'}}</view>\r\n\t\t\t\t\t\t\t\t<text class=\"option-text\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"option-check\" v-if=\"form.isLift === item.name\">✓</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"form-group modern-group\">\r\n\t\t\t\t\t\t<view class=\"group-header\">\r\n\t\t\t\t\t\t\t<text class=\"group-icon\">🚗</text>\r\n\t\t\t\t\t\t\t<text class=\"group-label\">停车位</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"option-grid binary-grid\">\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tclass=\"option-item modern-option binary-option\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'active': form.isParking === item.name }\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in parkingList\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t\t@click=\"form.isParking = item.name\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<view class=\"option-icon\">{{item.name === '是' ? '✅' : '❌'}}</view>\r\n\t\t\t\t\t\t\t\t<text class=\"option-text\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"option-check\" v-if=\"form.isParking === item.name\">✓</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 配置信息卡片 -->\r\n\t\t\t<view class=\"form-card modern-card\">\r\n\t\t\t\t<view class=\"card-header gradient-header config-header\">\r\n\t\t\t\t\t<view class=\"header-icon\">🛋️</view>\r\n\t\t\t\t\t<view class=\"header-text\">\r\n\t\t\t\t\t\t<text class=\"card-title\">房屋配置</text>\r\n\t\t\t\t\t\t<text class=\"card-subtitle\">选择房源提供的设施配置</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"config-grid\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"config-item modern-config\"\r\n\t\t\t\t\t\t\t:class=\"{ 'checked': item.checked }\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in configList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t@click=\"toggleConfig(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"config-icon-wrapper\">\r\n\t\t\t\t\t\t\t\t<text class=\"config-icon\">{{getConfigIcon(item.name)}}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"config-check\" v-if=\"item.checked\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"check-icon\">✓</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"config-text\">{{item.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 周边设施卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text class=\"card-title\">🌍 周边设施</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"checkbox-grid\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"checkbox-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'checked': item.checked }\"\r\n\t\t\t\t\t\t\tv-for=\"(item, index) in envList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t@click=\"toggleEnv(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"checkbox-icon\">{{item.checked ? '✓' : ''}}</text>\r\n\t\t\t\t\t\t\t<text class=\"checkbox-text\">{{item.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 房源详情卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text class=\"card-title\">📝 房源详情</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">房源标题</text>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\tv-model=\"form.title\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入房源标题\"\r\n\t\t\t\t\t\t\tmaxlength=\"50\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">房源简介</text>\r\n\t\t\t\t\t\t<textarea\r\n\t\t\t\t\t\t\tclass=\"custom-textarea\"\r\n\t\t\t\t\t\t\tv-model=\"form.info\"\r\n\t\t\t\t\t\t\tplaceholder=\"请详细描述房源特色、周边环境等信息\"\r\n\t\t\t\t\t\t\tmaxlength=\"500\"\r\n\t\t\t\t\t\t></textarea>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"input-row\">\r\n\t\t\t\t\t\t<view class=\"input-group half\">\r\n\t\t\t\t\t\t\t<text class=\"input-label\">租金(元/月)</text>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\t\tv-model=\"form.rent\"\r\n\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"0\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"input-group half\">\r\n\t\t\t\t\t\t\t<text class=\"input-label\">面积(m²)</text>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\t\tv-model=\"form.area\"\r\n\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"0\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">楼层</text>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\tv-model=\"form.floor\"\r\n\t\t\t\t\t\t\tplaceholder=\"如：3/6层\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 联系信息卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text class=\"card-title\">📞 联系信息</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">联系人</text>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\tv-model=\"form.name\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入联系人姓名\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">联系方式</text>\r\n\t\t\t\t\t\t<view class=\"phone-input-container\">\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"custom-input phone-input\"\r\n\t\t\t\t\t\t\t\tv-model=\"form.mobile\"\r\n\t\t\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入手机号\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tv-if=\"userInfo.mobile=='未绑定'||userInfo.mobile==null||userInfo.mobile==''\"\r\n\t\t\t\t\t\t\t\tclass=\"quick-input-btn\"\r\n\t\t\t\t\t\t\t\topen-type=\"getPhoneNumber\"\r\n\t\t\t\t\t\t\t\t@getphonenumber=\"getPhoneNumber\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t一键输入\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">微信号</text>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\tv-model=\"form.weixin\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入微信号（选填）\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"input-group\">\r\n\t\t\t\t\t\t<text class=\"input-label\">房源地址</text>\r\n\r\n\t\t\t\t\t\t<!-- 地图选择功能 -->\r\n\t\t\t\t\t\t<view class=\"address-selector\" @click=\"openMapSelector\">\r\n\t\t\t\t\t\t\t<view class=\"address-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"address-text\" :class=\"{ 'placeholder': !form.address }\">\r\n\t\t\t\t\t\t\t\t\t{{form.address || '点击选择地址'}}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"form.latitude && form.longitude\" class=\"address-coordinates\">\r\n\t\t\t\t\t\t\t\t\t经纬度: {{form.latitude.toFixed(6)}}, {{form.longitude.toFixed(6)}}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"address-icon\">📍</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 手动输入选项 -->\r\n\t\t\t\t\t\t<view class=\"manual-input-option\">\r\n\t\t\t\t\t\t\t<text class=\"manual-link\" @click=\"toggleManualInput\">\r\n\t\t\t\t\t\t\t\t{{ showManualInput ? '使用地图选择' : '手动输入地址' }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 手动输入框 -->\r\n\t\t\t\t\t\t<view v-if=\"showManualInput\" class=\"manual-address-input\">\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\tclass=\"custom-input\"\r\n\t\t\t\t\t\t\t\tv-model=\"form.address\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请输入详细地址（如：北京市朝阳区xxx小区x号楼）\"\r\n\t\t\t\t\t\t\t\t@input=\"clearCoordinates\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"address-tip\">\r\n\t\t\t\t\t\t\t<text class=\"tip-text\">💡 建议使用地图选择获得精确位置，或手动输入详细地址</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 房源图片卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<text class=\"card-title\">📷 房源图片</text>\r\n\t\t\t\t\t<text class=\"card-subtitle\">最多上传9张图片</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"image-upload-container\">\r\n\t\t\t\t\t\t<view v-if=\"form.pics.length === 0\" class=\"upload-placeholder\" @click=\"upImage\">\r\n\t\t\t\t\t\t\t<text class=\"upload-icon\">📷</text>\r\n\t\t\t\t\t\t\t<text class=\"upload-text\">点击上传图片</text>\r\n\t\t\t\t\t\t\t<text class=\"upload-hint\">支持多张图片上传</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else class=\"image-grid\">\r\n\t\t\t\t\t\t\t<view class=\"image-item add-image\" @click=\"upImage\">\r\n\t\t\t\t\t\t\t\t<text class=\"add-icon\">+</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\tclass=\"image-item\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in form.pics\"\r\n\t\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<image class=\"uploaded-image\" :src=\"item\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"image-delete\" @click=\"removeImage(index)\">×</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"form.pics.length > 0\" class=\"clear-all-btn\" @click=\"reset\">\r\n\t\t\t\t\t\t\t清空所有图片\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 提交按钮 -->\r\n\t\t\t<view class=\"submit-container\">\r\n\t\t\t\t<button class=\"submit-btn\" @click=\"submit\">\r\n\t\t\t\t\t<text class=\"submit-text\">发布房源</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<!-- 房型类型选择器弹窗 -->\r\n\t<view class=\"picker-overlay\" v-if=\"showTypeModal\" @click=\"hideTypePicker\">\r\n\t\t<view class=\"picker-modal\" @click.stop>\r\n\t\t\t<view class=\"picker-header\">\r\n\t\t\t\t<view class=\"picker-title\">\r\n\t\t\t\t\t<text class=\"picker-icon\">🏠</text>\r\n\t\t\t\t\t<text class=\"picker-title-text\">选择房型类型</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"picker-close\" @click=\"hideTypePicker\">✕</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"picker-content\">\r\n\t\t\t\t<view class=\"picker-grid\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"picker-item\"\r\n\t\t\t\t\t\t:class=\"{ 'active': tempType === item.name }\"\r\n\t\t\t\t\t\tv-for=\"(item, index) in typeList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t@click=\"selectType(item.name)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"picker-item-icon\">{{getTypeIcon(item.name)}}</view>\r\n\t\t\t\t\t\t<text class=\"picker-item-text\">{{item.name}}</text>\r\n\t\t\t\t\t\t<view class=\"picker-item-check\" v-if=\"tempType === item.name\">✓</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"picker-footer\">\r\n\t\t\t\t<button class=\"picker-btn cancel\" @click=\"hideTypePicker\">取消</button>\r\n\t\t\t\t<button class=\"picker-btn confirm\" @click=\"confirmType\">确定</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<!-- 床位朝向选择器弹窗 -->\r\n\t<view class=\"picker-overlay\" v-if=\"showDirectionModal\" @click=\"hideDirectionPicker\">\r\n\t\t<view class=\"picker-modal\" @click.stop>\r\n\t\t\t<view class=\"picker-header\">\r\n\t\t\t\t<view class=\"picker-title\">\r\n\t\t\t\t\t<text class=\"picker-icon\">🧭</text>\r\n\t\t\t\t\t<text class=\"picker-title-text\">选择床位朝向</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"picker-close\" @click=\"hideDirectionPicker\">✕</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"picker-content\">\r\n\t\t\t\t<view class=\"picker-grid\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"picker-item\"\r\n\t\t\t\t\t\t:class=\"{ 'active': form.direction === item.name }\"\r\n\t\t\t\t\t\tv-for=\"(item, index) in dirList\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t@click=\"selectDirection(item.name)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"picker-item-icon\">{{getDirectionIcon(item.name)}}</view>\r\n\t\t\t\t\t\t<text class=\"picker-item-text\">{{item.name}}</text>\r\n\t\t\t\t\t\t<view class=\"picker-item-check\" v-if=\"form.direction === item.name\">✓</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"picker-footer\">\r\n\t\t\t\t<button class=\"picker-btn cancel\" @click=\"hideDirectionPicker\">取消</button>\r\n\t\t\t\t<button class=\"picker-btn confirm\" @click=\"confirmDirection\">确定</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar vk = uni.vk;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\t// 页面数据变量\r\n\t\t\treturn {\r\n\t\t\t\tshowManualInput: false, // 是否显示手动输入\r\n\t\t\t\tshowTypeModal: false, // 是否显示房型选择弹窗\r\n\t\t\t\ttempType: '', // 临时选择的房型\r\n\t\t\t\tshowDirectionModal: false, // 是否显示朝向选择弹窗\r\n\t\t\t\ttempDirection: '', // 临时选择的朝向\r\n\t\t\t\tconfigList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '空调',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '洗衣机',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '热水器',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '冰箱',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: 'WiFi',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '床',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '衣柜',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '沙发',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '燃气灶',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tenvList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '地铁',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '公交站',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '超市',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '医院',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '学校',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\ttypeList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '整租一居',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '整租两居',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '合租单间',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tdirList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '东',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '南',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '西',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '北',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '南北通透',\r\n\t\t\t\t\t\tchecked: false,\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tliftList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '是',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '否',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\tparkingList:[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '是',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '否',\r\n\t\t\t\t\t\tdisabled: false\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\timage: \"/static/empty.png\",\r\n\t\t\t\tminDate: \"\",\r\n\t\t\t\tmaxDate: \"\",\r\n\t\t\t\tform: {\r\n\t\t\t\t\ttype: \"\",\r\n\t\t\t\t\tdirection:\"\",\r\n\t\t\t\t\tisLift:\"\",\r\n\t\t\t\t\tisParking:\"\",\r\n\t\t\t\t\ttitle:\"\",\r\n\t\t\t\t\trent:0,\r\n\t\t\t\t\tarea:\"\",\r\n\t\t\t\t\tname: \"\",\r\n\t\t\t\t\taddress:\"\",\r\n\t\t\t\t\tlatitude: null,\r\n\t\t\t\t\tlongitude: null,\r\n\t\t\t\t\tmobile: \"\",\r\n\t\t\t\t\tweixin: \"\",\r\n\t\t\t\t\tinfo: \"\",\r\n\t\t\t\t\tpics: []\r\n\t\t\t\t},\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tshow: false,\r\n\t\t\t\tshowManualInput: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 监听 - 页面每次【加载时】执行(如：前进)\r\n\t\tonLoad(options = {}) {\r\n\t\t\tvk = uni.vk;\r\n\t\t\tthis.options = options;\r\n\t\t\tthis.init(options);\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发\r\n\t\tonReady() {},\r\n\t\t// 监听 - 页面每次【显示时】执行（如：前进和返回）（页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面）\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时检查登录状态\r\n\t\t\tif (vk.checkToken()) {\r\n\t\t\t\t// 更新用户信息\r\n\t\t\t\tthis.userInfo = vk.getVuex('$user.userInfo');\r\n\t\t\t\tconsole.log(\"页面显示，用户已登录：\", this.userInfo);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(\"页面显示，用户未登录\");\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 监听 - 页面每次【隐藏时】执行（如：返回）\r\n\t\tonHide() {},\r\n\t\t// 监听 - 页面每次【卸载时】（一般用于取消页面上的监听器）\r\n\t\tonUnload() {},\r\n\t\t// 监听 - 页面下拉刷新\r\n\t\tonPullDownRefresh() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.form = {\r\n\t\t\t\t\ttype: \"\",\r\n\t\t\t\t\tdirection:\"\",\r\n\t\t\t\t\tisLift:\"\",\r\n\t\t\t\t\tisParking:\"\",\r\n\t\t\t\t\ttitle:\"\",\r\n\t\t\t\t\trent:0,\r\n\t\t\t\t\tarea:\"\",\r\n\t\t\t\t\tname: \"\",\r\n\t\t\t\t\taddress:\"\",\r\n\t\t\t\t\tlatitude: null,\r\n\t\t\t\t\tlongitude: null,\r\n\t\t\t\t\tmobile: \"\",\r\n\t\t\t\t\tweixin: \"\",\r\n\t\t\t\t\tinfo: \"\",\r\n\t\t\t\t\tpics: []\r\n\t\t\t\t}\r\n\t\t\t\tthis.resetSelections(); // 重置选择状态\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\t\t/**\r\n\t\t * 监听 - 点击右上角转发时 文档 https://uniapp.dcloud.io/api/plugins/share?id=onshareappmessage\r\n\t\t * 如果删除onShareAppMessage函数，则微信小程序右上角转发按钮会自动变灰\r\n\t\t */\r\n\t\tonShareAppMessage(options) {\r\n\r\n\t\t},\r\n\t\t// 函数\r\n\t\tmethods: {\r\n\t\t\t// 获取房型图标\r\n\t\t\tgetTypeIcon(type) {\r\n\t\t\t\tconst icons = {\r\n\t\t\t\t\t'整租一居': '🏠',\r\n\t\t\t\t\t'整租两居': '🏡',\r\n\t\t\t\t\t'合租单间': '🚪'\r\n\t\t\t\t};\r\n\t\t\t\treturn icons[type] || '🏠';\r\n\t\t\t},\r\n\r\n\t\t\t// 获取朝向图标\r\n\t\t\tgetDirectionIcon(direction) {\r\n\t\t\t\tconst icons = {\r\n\t\t\t\t\t'东': '🌅',\r\n\t\t\t\t\t'南': '☀️',\r\n\t\t\t\t\t'西': '🌇',\r\n\t\t\t\t\t'北': '❄️',\r\n\t\t\t\t\t'南北通透': '🌬️'\r\n\t\t\t\t};\r\n\t\t\t\treturn icons[direction] || '🧭';\r\n\t\t\t},\r\n\r\n\t\t\t// 获取配置图标\r\n\t\t\tgetConfigIcon(config) {\r\n\t\t\t\tconst icons = {\r\n\t\t\t\t\t'空调': '❄️',\r\n\t\t\t\t\t'洗衣机': '🧺',\r\n\t\t\t\t\t'热水器': '🚿',\r\n\t\t\t\t\t'冰箱': '🧊',\r\n\t\t\t\t\t'WiFi': '📶',\r\n\t\t\t\t\t'床': '🛏️',\r\n\t\t\t\t\t'衣柜': '👔',\r\n\t\t\t\t\t'沙发': '🛋️',\r\n\t\t\t\t\t'燃气灶': '🔥'\r\n\t\t\t\t};\r\n\t\t\t\treturn icons[config] || '🏠';\r\n\t\t\t},\r\n\r\n\t\t\t// 显示房型选择弹窗\r\n\t\t\tshowTypePicker() {\r\n\t\t\t\tthis.tempType = this.form.type; // 保存当前选择\r\n\t\t\t\tthis.showTypeModal = true;\r\n\t\t\t},\r\n\r\n\t\t\t// 隐藏房型选择弹窗\r\n\t\t\thideTypePicker() {\r\n\t\t\t\tthis.showTypeModal = false;\r\n\t\t\t\tthis.tempType = '';\r\n\t\t\t},\r\n\r\n\t\t\t// 选择房型\r\n\t\t\tselectType(type) {\r\n\t\t\t\tthis.tempType = type;\r\n\t\t\t},\r\n\r\n\t\t\t// 确认房型选择\r\n\t\t\tconfirmType() {\r\n\t\t\t\tif (this.tempType) {\r\n\t\t\t\t\tthis.form.type = this.tempType;\r\n\t\t\t\t}\r\n\t\t\t\tthis.hideTypePicker();\r\n\t\t\t},\r\n\r\n\t\t\t// 显示朝向选择弹窗\r\n\t\t\tshowDirectionPicker() {\r\n\t\t\t\tthis.tempDirection = this.form.direction; // 保存当前选择\r\n\t\t\t\tthis.showDirectionModal = true;\r\n\t\t\t},\r\n\r\n\t\t\t// 隐藏朝向选择弹窗\r\n\t\t\thideDirectionPicker() {\r\n\t\t\t\tthis.showDirectionModal = false;\r\n\t\t\t\tthis.tempDirection = '';\r\n\t\t\t},\r\n\r\n\t\t\t// 选择朝向\r\n\t\t\tselectDirection(direction) {\r\n\t\t\t\tthis.tempDirection = direction;\r\n\t\t\t},\r\n\r\n\t\t\t// 确认朝向选择\r\n\t\t\tconfirmDirection() {\r\n\t\t\t\tif (this.tempDirection) {\r\n\t\t\t\t\tthis.form.direction = this.tempDirection;\r\n\t\t\t\t}\r\n\t\t\t\tthis.hideDirectionPicker();\r\n\t\t\t},\r\n\r\n\t\t\t// 页面数据初始化函数\r\n\t\t\tinit(options = {}) {\r\n\t\t\t\tconsole.log(\"发布页面初始化\");\r\n\t\t\t\tthis.checkLoginStatus();\r\n\t\t\t},\r\n\t\t\tcheckboxChange(e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t},\r\n\t\t\tradioChange1(e) {\r\n\t\t\t\tthis.form.type = e\r\n\t\t\t},\r\n\t\t\tradioChange2(e) {\r\n\t\t\t\tthis.form.direction = e\r\n\t\t\t},\r\n\t\t\tradioChange3(e) {\r\n\t\t\t\tthis.form.isLift = e\r\n\t\t\t},\r\n\t\t\tradioChange4(e) {\r\n\t\t\t\tthis.form.isParking = e\r\n\t\t\t},\r\n\t\t\treset() {\r\n\t\t\t\tthis.form.pics = []\r\n\t\t\t},\r\n\t\t\tremoveImage(index) {\r\n\t\t\t\tthis.form.pics.splice(index, 1)\r\n\t\t\t},\r\n\t\t\tclearCoordinates() {\r\n\t\t\t\t// 手动输入地址时清除坐标信息\r\n\t\t\t\tthis.form.latitude = null;\r\n\t\t\t\tthis.form.longitude = null;\r\n\t\t\t},\r\n\r\n\t\t\t// 打开地图选择器\r\n\t\t\topenMapSelector() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/map/select/index?latitude=${this.form.latitude || ''}&longitude=${this.form.longitude || ''}`,\r\n\t\t\t\t\tevents: {\r\n\t\t\t\t\t\t// 监听地址选择事件\r\n\t\t\t\t\t\tselectAddress: (data) => {\r\n\t\t\t\t\t\t\tconsole.log('选择的地址：', data);\r\n\t\t\t\t\t\t\tthis.form.address = data.address;\r\n\t\t\t\t\t\t\tthis.form.latitude = data.latitude;\r\n\t\t\t\t\t\t\tthis.form.longitude = data.longitude;\r\n\r\n\t\t\t\t\t\t\t// 显示成功提示\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '地址选择成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 切换手动输入模式\r\n\t\t\ttoggleManualInput() {\r\n\t\t\t\tthis.showManualInput = !this.showManualInput;\r\n\t\t\t\tif (this.showManualInput) {\r\n\t\t\t\t\t// 切换到手动输入时，清除坐标信息\r\n\t\t\t\t\tthis.clearCoordinates();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoggleConfig(index) {\r\n\t\t\t\t// 切换房屋配置选择状态\r\n\t\t\t\tconsole.log('点击配置项：', index, this.configList[index].name);\r\n\t\t\t\tthis.$set(this.configList, index, {\r\n\t\t\t\t\t...this.configList[index],\r\n\t\t\t\t\tchecked: !this.configList[index].checked\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log('配置选择状态：', this.configList[index].name, this.configList[index].checked);\r\n\t\t\t\t// 强制更新视图\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\ttoggleEnv(index) {\r\n\t\t\t\t// 切换周边设施选择状态\r\n\t\t\t\tconsole.log('点击设施项：', index, this.envList[index].name);\r\n\t\t\t\tthis.$set(this.envList, index, {\r\n\t\t\t\t\t...this.envList[index],\r\n\t\t\t\t\tchecked: !this.envList[index].checked\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log('设施选择状态：', this.envList[index].name, this.envList[index].checked);\r\n\t\t\t\t// 强制更新视图\r\n\t\t\t\tthis.$forceUpdate();\r\n\t\t\t},\r\n\t\t\tresetSelections() {\r\n\t\t\t\t// 重置所有选择状态\r\n\t\t\t\tthis.configList.forEach((item, index) => {\r\n\t\t\t\t\tthis.$set(this.configList, index, {\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tchecked: false\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t\tthis.envList.forEach((item, index) => {\r\n\t\t\t\t\tthis.$set(this.envList, index, {\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tchecked: false\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcheckPrivacyAgreement(callback) {\r\n\t\t\t\t// 检查隐私协议同意状态\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 微信小程序隐私协议检查\r\n\t\t\t\t\tif (typeof wx !== 'undefined' && wx.requirePrivacyAuthorize) {\r\n\t\t\t\t\t\twx.requirePrivacyAuthorize({\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tconsole.log('隐私协议已同意');\r\n\t\t\t\t\t\t\t\tcallback && callback();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.error('隐私协议检查失败：', err);\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '隐私协议提示',\r\n\t\t\t\t\t\t\t\t\tcontent: '使用地图选择功能需要您同意隐私协议。请在弹出的隐私协议中点击\"同意\"后重试。',\r\n\t\t\t\t\t\t\t\t\tshowCancel: true,\r\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\t\t\tconfirmText: '重试',\r\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t// 用户点击重试，再次尝试\r\n\t\t\t\t\t\t\t\t\t\t\tthis.checkPrivacyAgreement(callback);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 非微信环境或旧版本，直接执行回调\r\n\t\t\t\t\t\tcallback && callback();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('隐私协议检查异常：', error);\r\n\t\t\t\t\t// 出现异常时显示手动输入选项\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: '无法使用地图选择功能，请使用手动输入地址。',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tthis.showManualInput = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tupImage() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 9,\r\n\t\t\t\t\tsizeType: ['compressed'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tres.tempFilePaths.forEach(item => {\r\n\t\t\t\t\t\t\tvk.uploadFile({\r\n\t\t\t\t\t\t\t\tfilePath: item,\r\n\t\t\t\t\t\t\t\tfileType: \"image\"\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tthat.form.pics.push(res.fileURL)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: function() {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsubmit() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tvar jsonData = that.form\r\n\t\t\t\tjsonData.configList = ''\r\n\t\t\t\tjsonData.envList = ''\r\n\t\t\t\tthis.configList.forEach(item=>{\r\n\t\t\t\t\tif(item.checked){\r\n\t\t\t\t\t\tjsonData.configList+=item.name+' '\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis.envList.forEach(item=>{\r\n\t\t\t\t\tif(item.checked){\r\n\t\t\t\t\t\tjsonData.envList+=item.name+' '\r\n\t\t\t\t\t}\r\n\t\t\t\t})\t\t\t\t\r\n\t\t\t\tif (!vk.pubfn.test(jsonData.mobile, 'mobile')) {\r\n\t\t\t\t\tvk.toast('请输入正确的手机号')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 检查登录状态\r\n\t\t\t\tif (!vk.checkToken()) {\r\n\t\t\t\t\tvk.toast('请先登录');\r\n\t\t\t\t\tthat.login();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tlet userInfo = vk.getVuex('$user.userInfo');\r\n\t\t\t\tif (!userInfo || !userInfo._id) {\r\n\t\t\t\t\tvk.toast('获取用户信息失败，请重新登录');\r\n\t\t\t\t\tthat.login();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tjsonData.user_id = userInfo._id;\r\n\t\t\t\tjsonData.status = \"0\"  // 默认状态为未审核\r\n\t\t\t\tjsonData.sort = 0\r\n\t\t\t\tconsole.log(jsonData)\r\n\t\t\t\tvk.callFunction({\r\n\t\t\t\t\turl: 'client/mall/goods/pub/add',\r\n\t\t\t\t\tdata: jsonData\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\tvk.toast('提交成功');\r\n\t\t\t\t\t\tvk.hideLoading();\r\n\t\t\t\t\t\tthat.form = {\r\n\t\t\t\t\t\t\ttype: \"\",\r\n\t\t\t\t\t\t\tdirection:\"\",\r\n\t\t\t\t\t\t\tisLift:\"\",\r\n\t\t\t\t\t\t\tisParking:\"\",\r\n\t\t\t\t\t\t\ttitle:\"\",\r\n\t\t\t\t\t\t\trent:0,\r\n\t\t\t\t\t\t\tarea:\"\",\r\n\t\t\t\t\t\t\tname: \"\",\r\n\t\t\t\t\t\t\taddress:\"\",\r\n\t\t\t\t\t\t\tlatitude: null,\r\n\t\t\t\t\t\t\tlongitude: null,\r\n\t\t\t\t\t\t\tmobile: \"\",\r\n\t\t\t\t\t\t\tweixin: \"\",\r\n\t\t\t\t\t\t\tinfo: \"\",\r\n\t\t\t\t\t\t\tpics: []\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.resetSelections(); // 重置选择状态\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 检查登录状态\r\n\t\t\tcheckLoginStatus() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconsole.log(\"检查登录状态\");\r\n\r\n\t\t\t\t// 检查是否已登录\r\n\t\t\t\tif (vk.checkToken()) {\r\n\t\t\t\t\t// 已登录，获取用户信息\r\n\t\t\t\t\tconsole.log(\"用户已登录，获取用户信息\");\r\n\t\t\t\t\tthat.userInfo = vk.getVuex('$user.userInfo');\r\n\t\t\t\t\tconsole.log(\"当前用户信息：\", that.userInfo);\r\n\r\n\t\t\t\t\t// 如果用户信息中有手机号，自动填入表单\r\n\t\t\t\t\tif (vk.pubfn.isNotNull(that.userInfo.mobile)) {\r\n\t\t\t\t\t\tthat.form.mobile = that.userInfo.mobile;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 未登录，需要登录\r\n\t\t\t\t\tconsole.log(\"用户未登录，跳转登录\");\r\n\t\t\t\t\tthat.login();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlogin() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tconsole.log(\"开始微信登录\");\r\n\r\n\t\t\t\twx.requirePrivacyAuthorize({\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t// 用户同意授权\r\n\t\t\t\t\t\tconsole.log(\"隐私授权成功，开始微信登录\");\r\n\t\t\t\t\t\tvk.userCenter.loginByWeixin({\r\n\t\t\t\t\t\t\tdata: {},\r\n\t\t\t\t\t\t\tsuccess: (data) => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"微信登录成功：\", data);\r\n\t\t\t\t\t\t\t\tthat.userInfo = data.userInfo;\r\n\r\n\t\t\t\t\t\t\t\t// 自动填入手机号\r\n\t\t\t\t\t\t\t\tif (vk.pubfn.isNotNull(data.userInfo.mobile)) {\r\n\t\t\t\t\t\t\t\t\tthat.form.mobile = data.userInfo.mobile;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tvk.toast(\"登录成功\");\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.error(\"微信登录失败：\", err);\r\n\t\t\t\t\t\t\t\tvk.toast(\"登录失败，请重试\");\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tconsole.log(\"用户拒绝隐私授权\");\r\n\t\t\t\t\t\tvk.toast(\"需要授权才能使用发布功能\");\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchooseAddress() {\r\n\t\t\t\t// 先检查隐私协议同意状态\r\n\t\t\t\tthis.checkPrivacyAgreement(() => {\r\n\t\t\t\t\t// 显示加载提示\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '打开地图中...',\r\n\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 调用地图选择位置API\r\n\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('选择的位置信息：', res);\r\n\r\n\t\t\t\t\t\t// 保存详细的地址信息\r\n\t\t\t\t\t\tthis.form.address = res.address || res.name || '未知地址';\r\n\t\t\t\t\t\tthis.form.latitude = res.latitude;\r\n\t\t\t\t\t\tthis.form.longitude = res.longitude;\r\n\r\n\t\t\t\t\t\t// 如果有详细地址，优先使用详细地址\r\n\t\t\t\t\t\tif (res.address && res.name && res.address !== res.name) {\r\n\t\t\t\t\t\t\tthis.form.address = `${res.name} (${res.address})`;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\t\t// 显示成功提示\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '地址选择成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('选择地址失败：', err);\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\t\t// 根据不同的错误类型显示不同的提示\r\n\t\t\t\t\t\tlet errorMsg = '选择地址失败';\r\n\t\t\t\t\t\tif (err.errMsg) {\r\n\t\t\t\t\t\t\tif (err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\t\t\terrorMsg = '已取消选择地址';\r\n\t\t\t\t\t\t\t} else if (err.errMsg.includes('auth') || err.errMsg.includes('permission')) {\r\n\t\t\t\t\t\t\t\terrorMsg = '请授权位置信息后重试';\r\n\t\t\t\t\t\t\t} else if (err.errMsg.includes('privacy') || err.errMsg.includes('scope')) {\r\n\t\t\t\t\t\t\t\terrorMsg = '请在小程序设置中同意位置权限使用';\r\n\t\t\t\t\t\t\t\t// 显示更详细的提示\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '位置权限提示',\r\n\t\t\t\t\t\t\t\t\tcontent: '使用地图选择功能需要位置权限。请在小程序右上角\"...\"菜单中找到\"设置\"，开启位置信息权限。',\r\n\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\tconfirmText: '我知道了'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t} else if (err.errMsg.includes('system')) {\r\n\t\t\t\t\t\t\t\terrorMsg = '系统错误，请稍后重试';\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetPhoneNumber(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// 微信新增了code参数，可以直接传code，不再需要传 encryptedData 和 iv\r\n\t\t\t\tlet {\r\n\t\t\t\t\tcode\r\n\t\t\t\t} = e.detail;\r\n\t\t\t\tif (!code) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tvk.userCenter.getPhoneNumber({\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tcode,\r\n\t\t\t\t\t\tencryptedKey: that.encryptedKey\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (data) => {\r\n\t\t\t\t\t\tthat.form.mobile = data.mobile\r\n\t\t\t\t\t\tthat.userInfo.mobile = data.mobile\r\n\t\t\t\t\t\tvk.callFunction({\r\n\t\t\t\t\t\t\turl: 'client/user/kh/update',\r\n\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\t_id: that.userInfo._id,\r\n\t\t\t\t\t\t\t\tmobile: data.mobile\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tpageTo(path) {\r\n\t\t\t\tvk.navigateTo(path);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 检查JSON对象中必填字段是否为空\r\n\t\t\t * @param {Object} data - 要检查的JSON数据\r\n\t\t\t * @param {Array<string>} requiredFields - 必填字段数组\r\n\t\t\t * @returns {Array<string>} - 为空的字段名称数组，若为空则表示所有必填字段都有值\r\n\t\t\t */\r\n\t\t\tcheckRequiredFields(data, requiredFields) {\r\n\t\t\t  const emptyFields = [];\r\n\t\t\t  \r\n\t\t\t  requiredFields.forEach(field => {\r\n\t\t\t    const value = data[field];\r\n\t\t\t    \r\n\t\t\t    // 判断值是否为空（空字符串、null、undefined）\r\n\t\t\t    if (value === '' || value === null || value === undefined) {\r\n\t\t\t      emptyFields.push(field);\r\n\t\t\t    }\r\n\t\t\t    \r\n\t\t\t    // 可选：检查空数组或空对象\r\n\t\t\t    if (Array.isArray(value) && value.length === 0) {\r\n\t\t\t      emptyFields.push(field);\r\n\t\t\t    }\r\n\t\t\t    \r\n\t\t\t    if (typeof value === 'object' && value !== null && Object.keys(value).length === 0) {\r\n\t\t\t      emptyFields.push(field);\r\n\t\t\t    }\r\n\t\t\t  });\r\n\t\t\t  \r\n\t\t\t  return emptyFields;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 监听器\r\n\t\twatch: {\r\n\r\n\t\t},\r\n\t\t// 计算属性\r\n\t\tcomputed: {\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\npage {\r\n\tbackground: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);\r\n}\r\n\r\n.publish-container {\r\n\tmin-height: 100vh;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n/* 页面头部样式 */\r\n.page-header {\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tpadding: 60rpx 40rpx 40rpx 40rpx;\r\n\tmargin-bottom: 20rpx;\r\n\r\n\t.header-content {\r\n\t\ttext-align: center;\r\n\r\n\t\t.header-title {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tfont-weight: 700;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\tmargin-bottom: 12rpx;\r\n\t\t}\r\n\r\n\t\t.header-subtitle {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\tline-height: 1.4;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 表单容器 */\r\n.form-container {\r\n\tpadding: 0 24rpx;\r\n}\r\n\r\n/* 卡片样式 */\r\n.form-card {\r\n\tbackground: #ffffff;\r\n\tborder-radius: 24rpx;\r\n\tmargin-bottom: 24rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\toverflow: hidden;\r\n\r\n\t.card-header {\r\n\t\tbackground: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n\t\tpadding: 24rpx 32rpx;\r\n\t\tborder-bottom: 1px solid #e2e8f0;\r\n\r\n\t\t.card-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #1e293b;\r\n\t\t}\r\n\r\n\t\t.card-subtitle {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #64748b;\r\n\t\t\tmargin-left: 16rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.card-content {\r\n\t\tpadding: 32rpx;\r\n\t}\r\n}\r\n\r\n/* 表单组样式 */\r\n.form-group {\r\n\tmargin-bottom: 32rpx;\r\n\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.group-label {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #374151;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n}\r\n\r\n/* 单选框网格 */\r\n.radio-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 12rpx;\r\n\r\n\t.radio-item {\r\n\t\tflex: 1;\r\n\t\tmin-width: 120rpx;\r\n\t\tpadding: 16rpx 24rpx;\r\n\t\tbackground: #f8fafc;\r\n\t\tborder: 2rpx solid #e2e8f0;\r\n\t\tborder-radius: 16rpx;\r\n\t\ttext-align: center;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tcursor: pointer;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.98);\r\n\t\t}\r\n\r\n\t\t&.active {\r\n\t\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\t\tborder-color: #667eea;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\r\n\r\n\t\t\t.radio-text {\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.radio-text {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #64748b;\r\n\t\t\ttransition: color 0.3s ease;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 复选框网格 */\r\n.checkbox-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 12rpx;\r\n\r\n\t.checkbox-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 16rpx 20rpx;\r\n\t\tbackground: #f8fafc;\r\n\t\tborder: 2rpx solid #e2e8f0;\r\n\t\tborder-radius: 16rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tmin-width: 120rpx;\r\n\t\tcursor: pointer;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.98);\r\n\t\t}\r\n\r\n\t\t&.checked {\r\n\t\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\t\tborder-color: #667eea;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\r\n\r\n\t\t\t.checkbox-text {\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\r\n\t\t\t.checkbox-icon {\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.checkbox-icon {\r\n\t\t\twidth: 32rpx;\r\n\t\t\theight: 32rpx;\r\n\t\t\tmargin-right: 12rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: transparent;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t}\r\n\r\n\t\t.checkbox-text {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #64748b;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 输入框样式 */\r\n.input-group {\r\n\tmargin-bottom: 32rpx;\r\n\r\n\t&.half {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.input-label {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #374151;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n}\r\n\r\n.input-row {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.custom-input {\r\n\twidth: 100%;\r\n\tpadding: 20rpx 24rpx;\r\n\tbackground: #f8fafc;\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #1e293b;\r\n\ttransition: all 0.3s ease;\r\n\r\n\t&:focus {\r\n\t\tborder-color: #667eea;\r\n\t\tbackground: #ffffff;\r\n\t\tbox-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\r\n\t}\r\n}\r\n\r\n.custom-textarea {\r\n\twidth: 100%;\r\n\tmin-height: 160rpx;\r\n\tpadding: 20rpx 24rpx;\r\n\tbackground: #f8fafc;\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #1e293b;\r\n\tline-height: 1.5;\r\n\tresize: none;\r\n\ttransition: all 0.3s ease;\r\n\r\n\t&:focus {\r\n\t\tborder-color: #667eea;\r\n\t\tbackground: #ffffff;\r\n\t\tbox-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\r\n\t}\r\n}\r\n\r\n/* 手机号输入容器 */\r\n.phone-input-container {\r\n\tdisplay: flex;\r\n\tgap: 16rpx;\r\n\talign-items: center;\r\n\r\n\t.phone-input {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.quick-input-btn {\r\n\t\tpadding: 20rpx 24rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tcolor: #ffffff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 16rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: 500;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n}\r\n\r\n/* 地址选择器 */\r\n.address-selector {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 20rpx 24rpx;\r\n\tbackground: #f8fafc;\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\ttransition: all 0.3s ease;\r\n\tmin-height: 80rpx;\r\n\r\n\t&:active {\r\n\t\tbackground: #e2e8f0;\r\n\t}\r\n\r\n\t.address-content {\r\n\t\tflex: 1;\r\n\r\n\t\t.address-text {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #1e293b;\r\n\t\t\tline-height: 1.4;\r\n\t\t\tmargin-bottom: 4rpx;\r\n\r\n\t\t\t&.placeholder {\r\n\t\t\t\tcolor: #9ca3af;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.address-coordinates {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #64748b;\r\n\t\t\topacity: 0.8;\r\n\t\t}\r\n\t}\r\n\r\n\t.address-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-left: 16rpx;\r\n\t\tcolor: #667eea;\r\n\t}\r\n}\r\n\r\n/* 手动输入提示 */\r\n.manual-input-tip {\r\n\tmargin-top: 12rpx;\r\n\ttext-align: center;\r\n\r\n\t.tip-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #667eea;\r\n\t\ttext-decoration: underline;\r\n\t}\r\n}\r\n\r\n.manual-input-option {\r\n\tmargin-top: 16rpx;\r\n\ttext-align: center;\r\n\r\n\t.manual-link {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #007AFF;\r\n\t\ttext-decoration: underline;\r\n\t\tpadding: 12rpx;\r\n\r\n\t\t&:active {\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.manual-address-input {\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n/* 地址提示样式 */\r\n.address-tip {\r\n\tmargin-top: 12rpx;\r\n\tpadding: 16rpx;\r\n\tbackground: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n\tborder-radius: 12rpx;\r\n\tborder-left: 4rpx solid #0ea5e9;\r\n\r\n\t.tip-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #0369a1;\r\n\t\tline-height: 1.4;\r\n\t}\r\n}\r\n\r\n/* 图片上传样式 */\r\n.image-upload-container {\r\n\t.upload-placeholder {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 80rpx 40rpx;\r\n\t\tbackground: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n\t\tborder: 3rpx dashed #cbd5e1;\r\n\t\tborder-radius: 20rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\tbackground: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);\r\n\t\t}\r\n\r\n\t\t.upload-icon {\r\n\t\t\tfont-size: 80rpx;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t}\r\n\r\n\t\t.upload-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #475569;\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t}\r\n\r\n\t\t.upload-hint {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #64748b;\r\n\t\t}\r\n\t}\r\n\r\n\t.image-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 16rpx;\r\n\r\n\t\t.image-item {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t&.add-image {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbackground: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n\t\t\t\tborder: 3rpx dashed #cbd5e1;\r\n\r\n\t\t\t\t.add-icon {\r\n\t\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\t\tcolor: #64748b;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.uploaded-image {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.image-delete {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 8rpx;\r\n\t\t\t\tright: 8rpx;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: rgba(239, 68, 68, 0.9);\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.clear-all-btn {\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 16rpx 32rpx;\r\n\t\tbackground: #fee2e2;\r\n\t\tcolor: #dc2626;\r\n\t\tborder-radius: 12rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 500;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\tbackground: #fecaca;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 提交按钮 */\r\n.submit-container {\r\n\tpadding: 40rpx 24rpx;\r\n\r\n\t.submit-btn {\r\n\t\twidth: 100%;\r\n\t\tpadding: 24rpx;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tborder: none;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: translateY(2rpx);\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\r\n\t\t}\r\n\r\n\t\t.submit-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #ffffff;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* ========== 现代化设计样式 ========== */\r\n\r\n/* 现代化卡片增强 */\r\n.modern-card {\r\n\tborder: 1rpx solid #f1f5f9;\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n/* 渐变头部 */\r\n.gradient-header {\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 24rpx 32rpx !important;\r\n\r\n\t.header-icon {\r\n\t\tfont-size: 48rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.header-text {\r\n\t\tflex: 1;\r\n\r\n\t\t.card-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: white !important;\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t}\r\n\r\n\t\t.card-subtitle {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8) !important;\r\n\t\t\tmargin-left: 0 !important;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.config-header {\r\n\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n/* 现代化表单组 */\r\n.modern-group {\r\n\tmargin-bottom: 40rpx;\r\n\r\n\t.group-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t.group-icon {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tmargin-right: 12rpx;\r\n\t\t}\r\n\r\n\t\t.group-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #374151;\r\n\t\t\tmargin-bottom: 0 !important;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 现代化选项网格 */\r\n.option-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.binary-grid {\r\n\tgap: 20rpx;\r\n}\r\n\r\n/* 现代化选项项 */\r\n.modern-option {\r\n\tposition: relative;\r\n\tflex: 1;\r\n\tmin-width: 140rpx;\r\n\tpadding: 20rpx 16rpx;\r\n\tbackground: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\ttext-align: center;\r\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\tcursor: pointer;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n\r\n\t.option-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.option-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #475569;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.option-check {\r\n\t\tposition: absolute;\r\n\t\ttop: -8rpx;\r\n\t\tright: -8rpx;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: white;\r\n\t\tfont-weight: bold;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);\r\n\t}\r\n\r\n\t&:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t&.active {\r\n\t\tbackground: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);\r\n\t\tborder-color: #3b82f6;\r\n\t\tcolor: #1e40af;\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.2);\r\n\r\n\t\t.option-text {\r\n\t\t\tcolor: #1e40af;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.binary-option {\r\n\tflex: 0 0 calc(50% - 10rpx);\r\n\tmin-width: auto;\r\n}\r\n\r\n/* 现代化配置网格 */\r\n.config-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 16rpx;\r\n}\r\n\r\n/* 现代化配置项 */\r\n.modern-config {\r\n\tposition: relative;\r\n\tflex: 0 0 calc(25% - 12rpx);\r\n\tmin-width: 120rpx;\r\n\tpadding: 24rpx 16rpx;\r\n\tbackground: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 20rpx;\r\n\ttext-align: center;\r\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\tcursor: pointer;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);\r\n\r\n\t.config-icon-wrapper {\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 12rpx;\r\n\r\n\t\t.config-icon {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t\t.config-check {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: -8rpx;\r\n\t\t\tright: 50%;\r\n\t\t\ttransform: translateX(50%);\r\n\t\t\twidth: 28rpx;\r\n\t\t\theight: 28rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);\r\n\r\n\t\t\t.check-icon {\r\n\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\tcolor: white;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.config-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #475569;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t&.checked {\r\n\t\tbackground: linear-gradient(145deg, #ecfdf5 0%, #d1fae5 100%);\r\n\t\tborder-color: #10b981;\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 12rpx 32rpx rgba(16, 185, 129, 0.15);\r\n\r\n\t\t.config-text {\r\n\t\t\tcolor: #047857;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 响应式适配 */\r\n@media (max-width: 750rpx) {\r\n\t.modern-config {\r\n\t\tflex: 0 0 calc(33.333% - 11rpx);\r\n\t}\r\n}\r\n\r\n@media (max-width: 600rpx) {\r\n\t.modern-config {\r\n\t\tflex: 0 0 calc(50% - 8rpx);\r\n\t}\r\n}\r\n\r\n/* ========== 选择器样式 ========== */\r\n\r\n/* 选择器显示区域 */\r\n.selector-container {\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.selector-display {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 20rpx 24rpx;\r\n\tbackground: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\r\n\t.selector-icon {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 16rpx;\r\n\t}\r\n\r\n\t.selector-text {\r\n\t\tflex: 1;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #374151;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.selector-arrow {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #9ca3af;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t&:hover {\r\n\t\tborder-color: #3b82f6;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);\r\n\r\n\t\t.selector-arrow {\r\n\t\t\ttransform: rotate(180deg);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 弹窗遮罩 */\r\n.picker-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 9999;\r\n\tanimation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n\tfrom { opacity: 0; }\r\n\tto { opacity: 1; }\r\n}\r\n\r\n/* 弹窗主体 */\r\n.picker-modal {\r\n\twidth: 600rpx;\r\n\tmax-width: 90vw;\r\n\tbackground: white;\r\n\tborder-radius: 24rpx;\r\n\toverflow: hidden;\r\n\tanimation: slideUp 0.3s ease;\r\n\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n@keyframes slideUp {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(100rpx) scale(0.9);\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t\ttransform: translateY(0) scale(1);\r\n\t}\r\n}\r\n\r\n/* 弹窗头部 */\r\n.picker-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 32rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n\r\n\t.picker-title {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.picker-icon {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-right: 16rpx;\r\n\t\t}\r\n\r\n\t\t.picker-title-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n\r\n\t.picker-close {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tfont-size: 28rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: rgba(255, 255, 255, 0.3);\r\n\t\t\ttransform: rotate(90deg);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 弹窗内容 */\r\n.picker-content {\r\n\tpadding: 32rpx;\r\n}\r\n\r\n.picker-grid {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.picker-item {\r\n\tposition: relative;\r\n\tflex: 0 0 calc(50% - 8rpx);\r\n\tpadding: 24rpx 20rpx;\r\n\tbackground: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\ttext-align: center;\r\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\tcursor: pointer;\r\n\r\n\t.picker-item-icon {\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-bottom: 12rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.picker-item-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #475569;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.picker-item-check {\r\n\t\tposition: absolute;\r\n\t\ttop: -8rpx;\r\n\t\tright: -8rpx;\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: linear-gradient(135deg, #10b981 0%, #059669 100%);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: white;\r\n\t\tfont-weight: bold;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);\r\n\t}\r\n\r\n\t&:hover {\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t&.active {\r\n\t\tbackground: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);\r\n\t\tborder-color: #3b82f6;\r\n\t\ttransform: translateY(-4rpx);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.2);\r\n\r\n\t\t.picker-item-text {\r\n\t\t\tcolor: #1e40af;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 弹窗底部 */\r\n.picker-footer {\r\n\tdisplay: flex;\r\n\tgap: 16rpx;\r\n\tpadding: 24rpx 32rpx 32rpx;\r\n\tbackground: #f8fafc;\r\n}\r\n\r\n.picker-btn {\r\n\tflex: 1;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tborder: none;\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\r\n\t&.cancel {\r\n\t\tbackground: #f1f5f9;\r\n\t\tcolor: #64748b;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: #e2e8f0;\r\n\t\t\tcolor: #475569;\r\n\t\t}\r\n\t}\r\n\r\n\t&.confirm {\r\n\t\tbackground: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\r\n\t\tcolor: white;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\r\n\t\t\ttransform: translateY(-2rpx);\r\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);\r\n\t\t}\r\n\t}\r\n}\r\n}\r\n</style>", "import mod from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}