{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/web/wxproject/预约租房/pages/tabbar/my/index.vue?9230", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/my/index.vue?7ac6", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/my/index.vue?57ce", "uni-app:///pages/tabbar/my/index.vue", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/my/index.vue?dbc4", "webpack:///D:/web/wxproject/预约租房/pages/tabbar/my/index.vue?563d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "titleStyle", "paddingLeft", "userInfo", "avatar", "nickname", "mobile", "is<PERSON>ogin", "onPageScroll", "onLoad", "vk", "onReady", "onShow", "onHide", "onUnload", "onPullDownRefresh", "setTimeout", "success", "that", "uni", "onShareAppMessage", "methods", "init", "console", "login", "fail", "complete", "loginOut", "pageTo", "handleUserCardClick", "switchToPublish", "title", "icon", "url", "watch", "computed"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC4K;AAC5K,gBAAgB,gLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,8nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkJnqB;AAAA,eACA;EACAC;IACA;IACA;MACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACA;EACAC;IAAA;IACAC;IACA;IACA;EACA;EACA;EACAC;EACA;EACAC,2BAEA;EACA;EACAC;EACA;EACAC;EACA;EACAC;IACA;IACAC;MACAN;QACAO;UACAC;QACA;MACA;MACAC;IACA;EACA;EACA;AACA;AACA;AACA;EACAC,wDAEA;EACA;EACAC;IACA;IACAC;MAAA;MACAC;MACA;MACA;QACA;QACAL;QACAA;QACAK;MACA;QACAL;QACAK;QACAL;MACA;IACA;IACAM;MACA;MACA;QACA7B;UACAsB;YACA;YACA;YACAP;cACAV;cACAiB;gBACAC;cACA;YACA;UACA;UACAO;YACAF;UACA;UAAA;UACAG;QACA;MACA;QACAhB;MACA;IACA;IACAiB;MACA;QACAvB;QACAC;QACAC;MACA;MACAI;MACAA;MACAA;IACA;IACAkB;MACAlB;IACA;IAEA;IACAmB;MACA;MACA;QACA;QACAnB;MACA;QACA;QACAA;MACA;IACA;IACA;IACAoB;MACAP;;MAEA;MACA;MACAA;MAEA;QACAA;QACAJ;UACAY;UACAC;QACA;QACA;QACAtB;QACA;MACA;MAEAa;;MAEA;MACAA;MAEAJ;QACAc;QACAhB;UACAM;QACA;QACAE;UACAF;UACAJ;YACAY;YACAC;UACA;QACA;MACA;IACA;EACA;EACA;EACAE,QAEA;EACA;EACAC,WAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACxTA;AAAA;AAAA;AAAA;AAAsvC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA1wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/my/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/tabbar/my/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=624165d7&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=624165d7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"624165d7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/my/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=624165d7&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/vk-uview-ui/components/u-avatar/u-avatar\" */ \"@/uni_modules/vk-uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/vk-uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/vk-uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page-container\">\r\n\t\t<!-- 顶部用户信息区域 -->\r\n\t\t<view class=\"header-section\">\r\n\t\t\t<view class=\"user-card\" @click=\"handleUserCardClick\">\r\n\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t<u-avatar\r\n\t\t\t\t\t\t:src=\"userInfo.avatar ? userInfo.avatar : '/static/default.png'\"\r\n\t\t\t\t\t\tsize=\"120\"\r\n\t\t\t\t\t\t:fade=\"true\"\r\n\t\t\t\t\t\tduration=\"450\"\r\n\t\t\t\t\t\t:lazy-load=\"true\"\r\n\t\t\t\t\t></u-avatar>\r\n\t\t\t\t\t<view class=\"avatar-border\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t<view class=\"welcome-text\">\r\n\t\t\t\t\t\t<text class=\"greeting\">👋 欢迎您</text>\r\n\t\t\t\t\t\t<text class=\"username\">{{ userInfo.nickname || '您还未登录' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"user-detail\">\r\n\t\t\t\t\t\t<text v-if=\"!userInfo._id\" class=\"complete-info\">\r\n\t\t\t\t\t\t\t✨ 点击完善资料\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<text v-else-if=\"userInfo.mobile == '未绑定' || userInfo.mobile == null || userInfo.mobile == ''\" class=\"complete-info\">\r\n\t\t\t\t\t\t\t✨ 点击完善资料\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<text v-else class=\"phone-info\">\r\n\t\t\t\t\t\t\t📱 {{ userInfo.mobile }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"arrow-icon\">\r\n\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#fff\" size=\"28\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要功能区域 -->\r\n\t\t<view class=\"main-content\">\r\n\t\t\t<!-- 房源管理 -->\r\n\t\t\t<view class=\"section-card\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t\t<text class=\"section-icon\">🏠</text>\r\n\t\t\t\t\t\t<text class=\"section-name\">房源管理</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"menu-grid\">\r\n\t\t\t\t\t<view class=\"menu-item\" @click=\"switchToPublish\">\r\n\t\t\t\t\t\t<view class=\"menu-icon publish-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"plus-circle\" color=\"#fff\" size=\"32\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"menu-title\">发布房源</text>\r\n\t\t\t\t\t\t<text class=\"menu-desc\">快速发布</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"menu-item\" @click=\"pageTo('/pages/my/publish/index')\">\r\n\t\t\t\t\t\t<view class=\"menu-icon manage-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"home\" color=\"#fff\" size=\"32\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"menu-title\">我的发布</text>\r\n\t\t\t\t\t\t<text class=\"menu-desc\">管理房源</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"menu-item\" @click=\"pageTo('/pages/my/order/index')\">\r\n\t\t\t\t\t\t<view class=\"menu-icon order-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"order\" color=\"#fff\" size=\"32\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"menu-title\">预约订单</text>\r\n\t\t\t\t\t\t<text class=\"menu-desc\">查看订单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"menu-item\" @click=\"pageTo('/pages/my/fav/index')\">\r\n\t\t\t\t\t\t<view class=\"menu-icon fav-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"bookmark\" color=\"#fff\" size=\"32\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"menu-title\">我的收藏</text>\r\n\t\t\t\t\t\t<text class=\"menu-desc\">收藏列表</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 服务支持 -->\r\n\t\t\t<view class=\"section-card\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t\t<text class=\"section-icon\">🛠️</text>\r\n\t\t\t\t\t\t<text class=\"section-name\">服务支持</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"service-list\">\r\n\t\t\t\t\t<view class=\"service-item\" @click=\"pageTo('/pages/my/feedback/index')\">\r\n\t\t\t\t\t\t<view class=\"service-icon feedback-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"edit-pen\" color=\"#667eea\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"service-content\">\r\n\t\t\t\t\t\t\t<text class=\"service-title\">投诉建议</text>\r\n\t\t\t\t\t\t\t<text class=\"service-desc\">意见反馈</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 法律条款 -->\r\n\t\t\t<view class=\"section-card\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t\t<text class=\"section-icon\">📋</text>\r\n\t\t\t\t\t\t<text class=\"section-name\">法律条款</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"service-list\">\r\n\t\t\t\t\t<view class=\"service-item\" @click=\"pageTo('/pages/page/index?key=yszc')\">\r\n\t\t\t\t\t\t<view class=\"service-icon privacy-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"file-text\" color=\"#10b981\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"service-content\">\r\n\t\t\t\t\t\t\t<text class=\"service-title\">用户隐私</text>\r\n\t\t\t\t\t\t\t<text class=\"service-desc\">隐私政策</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"service-item\" @click=\"pageTo('/pages/page/index?key=yhxy')\">\r\n\t\t\t\t\t\t<view class=\"service-icon agreement-icon\">\r\n\t\t\t\t\t\t\t<u-icon name=\"file-text\" color=\"#f59e0b\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"service-content\">\r\n\t\t\t\t\t\t\t<text class=\"service-title\">用户协议</text>\r\n\t\t\t\t\t\t\t<text class=\"service-desc\">服务条款</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 退出登录 -->\r\n\t\t\t<view class=\"logout-section\">\r\n\t\t\t\t<view class=\"logout-btn\" @click=\"loginOut\">\r\n\t\t\t\t\t<u-icon name=\"info-circle-fill\" color=\"#ef4444\" size=\"28\"></u-icon>\r\n\t\t\t\t\t<text class=\"logout-text\">退出登录</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar vk = uni.vk;\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\t// 页面数据变量\r\n\t\t\treturn {\r\n\t\t\t\ttitleStyle: {\r\n\t\t\t\t\tpaddingLeft:'20rpx'\r\n\t\t\t\t },\r\n\t\t\t\tuserInfo:{\r\n\t\t\t\t\tavatar:'/static/default.png',\r\n\t\t\t\t\tnickname:'游客',\r\n\t\t\t\t\tmobile:\"\"\r\n\t\t\t\t},\r\n\t\t\t\tisLogin: false // 登录状态标识\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPageScroll(e) {\r\n\t\t\tthis.scrollTop = e.scrollTop;\r\n\t\t},\r\n\t\t// 监听 - 页面每次【加载时】执行(如：前进)\r\n\t\tonLoad(options = {}) {\r\n\t\t\tvk = uni.vk;\r\n\t\t\tthis.options = options;\r\n\t\t\tthis.init(options);\r\n\t\t},\r\n\t\t// 监听 - 页面【首次渲染完成时】执行。注意如果渲染速度快，会在页面进入动画完成前触发\r\n\t\tonReady(){},\r\n\t\t// 监听 - 页面每次【显示时】执行（如：前进和返回）（页面每次出现在屏幕上都触发，包括从下级页面点返回露出当前页面）\r\n\t\tonShow() {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 监听 - 页面每次【隐藏时】执行（如：返回）\r\n\t\tonHide() {},\r\n\t\t// 监听 - 页面每次【卸载时】（一般用于取消页面上的监听器）\r\n\t\tonUnload(){},\r\n\t\t// 监听 - 页面下拉刷新\r\n\t\tonPullDownRefresh() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tvk.userCenter.getCurrentUserInfo({\r\n\t\t\t\t\t  success: (res) => {\r\n\t\t\t\t\t\t that.userInfo = vk.getVuex('$user.userInfo')\r\n\t\t\t\t\t  }\r\n\t\t\t\t\t});\t\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t/**\r\n\t\t * 监听 - 点击右上角转发时 文档 https://uniapp.dcloud.io/api/plugins/share?id=onshareappmessage\r\n\t\t * 如果删除onShareAppMessage函数，则微信小程序右上角转发按钮会自动变灰\r\n\t\t */\r\n\t\tonShareAppMessage(options) {\r\n\t\t\t\r\n\t\t},\r\n\t\t// 函数\r\n\t\tmethods: {\r\n\t\t\t// 页面数据初始化函数\r\n\t\t\tinit(options={}){\r\n\t\t\t\tconsole.log(\"我的页面初始化: \", options);\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (vk.checkToken()) {\r\n\t\t\t\t  // token有效\r\n\t\t\t\t  that.isLogin = true;\r\n\t\t\t\t  that.userInfo = vk.getVuex('$user.userInfo');\r\n\t\t\t\t  console.log(\"用户已登录，用户信息:\", that.userInfo);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.isLogin = false;\r\n\t\t\t\t\tconsole.log(\"用户未登录\");\r\n\t\t\t\t\tthat.login();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlogin() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif(that.isLogin){\r\n\t\t\t\t\twx.requirePrivacyAuthorize({\r\n\t\t\t\t\t\t success: () => {\r\n\t\t\t\t\t\t   // 用户同意授权\r\n\t\t\t\t\t\t   // 继续小程序逻辑\r\n\t\t\t\t\t\t   vk.userCenter.loginByWeixin({\r\n\t\t\t\t\t\t\t\tdata:{},\r\n\t\t\t\t\t\t\t\tsuccess: (data) => {\r\n\t\t\t\t\t\t\t\t\tthat.userInfo = data.userInfo\r\n\t\t\t\t\t\t\t\t} \r\n\t\t\t\t\t\t\t   });\r\n\t\t\t\t\t\t },\r\n\t\t\t\t\t\t fail: () => {\r\n\t\t\t\t\t\t\tconsole.log(\"拒绝\")\r\n\t\t\t\t\t\t }, // 用户拒绝授权\r\n\t\t\t\t\t\t complete: () => {}\r\n\t\t\t\t\t   })\r\n\t\t\t\t}else{\r\n\t\t\t\t\tvk.navigateTo('/pages/auth/index');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tloginOut() {\r\n\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\tavatar:'/static/default.png',\r\n\t\t\t\t\tnickname:'您还未登录',\r\n\t\t\t\t\tmobile:\"未绑定\"\r\n\t\t\t\t}\r\n\t\t\t\tvk.setVuex('$user.userInfo', {})\r\n\t\t\t\tvk.userCenter.logout()\r\n\t\t\t\tvk.toast('您已经成功退出!')\r\n\t\t\t},\r\n\t\t\tpageTo(path) {\r\n\t\t\t\tvk.navigateTo(path);\r\n\t\t\t},\r\n\r\n\t\t\t// 处理用户卡片点击\r\n\t\t\thandleUserCardClick() {\r\n\t\t\t\tconst userInfo = vk.getVuex('$user.userInfo');\r\n\t\t\t\tif (!userInfo || !userInfo._id) {\r\n\t\t\t\t\t// 未登录，跳转到新的登录注册页面\r\n\t\t\t\t\tvk.navigateTo('/pages/auth/index');\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 已登录，跳转到用户信息编辑页面\r\n\t\t\t\t\tvk.navigateTo('/pages/userInfo/index');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 切换到发布房源页面（底部tabbar）\r\n\t\t\tswitchToPublish() {\r\n\t\t\t\tconsole.log('=== 发布房源按钮被点击 ===');\r\n\r\n\t\t\t\t// 检查登录状态\r\n\t\t\t\tconst isLoggedIn = vk.checkToken();\r\n\t\t\t\tconsole.log('登录状态检查:', isLoggedIn);\r\n\r\n\t\t\t\tif (!isLoggedIn) {\r\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页面');\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请先登录',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 跳转到登录页面\r\n\t\t\t\t\tvk.navigateTo('/pages/auth/index');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('用户已登录，切换到发布房源页面');\r\n\r\n\t\t\t\t// 跳转到发布房源页面\r\n\t\t\t\tconsole.log('准备跳转到发布房源页面');\r\n\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/tabbar/add/index',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('成功跳转到发布房源页面');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '跳转失败，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\t// 监听器\r\n\t\twatch:{\r\n\t\t\t\r\n\t\t},\r\n\t\t// 计算属性\r\n\t\tcomputed:{\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tposition: relative;\r\n}\r\n\r\n/* 顶部用户信息区域 */\r\n.header-section {\r\n\tpadding: 60rpx 40rpx 40rpx;\r\n\tposition: relative;\r\n\r\n\t&::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n}\r\n\r\n.user-card {\r\n\tposition: relative;\r\n\tz-index: 2;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground: rgba(255, 255, 255, 0.15);\r\n\tbackdrop-filter: blur(20rpx);\r\n\tborder-radius: 24rpx;\r\n\tpadding: 32rpx;\r\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.avatar-container {\r\n\tposition: relative;\r\n\tmargin-right: 24rpx;\r\n\r\n\t.avatar-border {\r\n\t\tposition: absolute;\r\n\t\ttop: -6rpx;\r\n\t\tleft: -6rpx;\r\n\t\tright: -6rpx;\r\n\t\tbottom: -6rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.5));\r\n\t\tz-index: -1;\r\n\t}\r\n}\r\n\r\n.user-info {\r\n\tflex: 1;\r\n\r\n\t.welcome-text {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-bottom: 12rpx;\r\n\r\n\t\t.greeting {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t}\r\n\r\n\t\t.username {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.user-detail {\r\n\t\t.complete-info {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #fbbf24;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\r\n\t\t.phone-info {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.arrow-icon {\r\n\topacity: 0.8;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.main-content {\r\n\tflex: 1;\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 32rpx 32rpx 0 0;\r\n\tmargin-top: -20rpx;\r\n\tpadding: 40rpx 24rpx 120rpx;\r\n\tposition: relative;\r\n\tz-index: 1;\r\n}\r\n\r\n/* 卡片样式 */\r\n.section-card {\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 32rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.section-header {\r\n\tpadding: 32rpx 32rpx 24rpx;\r\n\tborder-bottom: 1rpx solid #f1f3f4;\r\n\r\n\t.section-title {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.section-icon {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tmargin-right: 16rpx;\r\n\t\t}\r\n\r\n\t\t.section-name {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #1a1a1a;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/* 网格菜单 */\r\n.menu-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(2, 1fr);\r\n\tgap: 24rpx;\r\n\tpadding: 32rpx;\r\n}\r\n\r\n.menu-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tpadding: 32rpx 24rpx;\r\n\tbackground: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n\tborder-radius: 16rpx;\r\n\ttransition: all 0.3s ease;\r\n\r\n\t&:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbackground: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\r\n\t}\r\n}\r\n\r\n.menu-icon {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 16rpx;\r\n\r\n\t&.publish-icon {\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t}\r\n\r\n\t&.manage-icon {\r\n\t\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n\t}\r\n\r\n\t&.order-icon {\r\n\t\tbackground: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n\t}\r\n\r\n\t&.fav-icon {\r\n\t\tbackground: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n\t}\r\n}\r\n\r\n.menu-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #1a1a1a;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.menu-desc {\r\n\tfont-size: 22rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 服务列表 */\r\n.service-list {\r\n\tpadding: 0 32rpx 16rpx;\r\n}\r\n\r\n.service-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 24rpx 0;\r\n\tborder-bottom: 1rpx solid #f1f3f4;\r\n\r\n\t&:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t&:active {\r\n\t\tbackground: #f8f9fa;\r\n\t\tmargin: 0 -32rpx;\r\n\t\tpadding-left: 32rpx;\r\n\t\tpadding-right: 32rpx;\r\n\t}\r\n}\r\n\r\n.service-icon {\r\n\twidth: 64rpx;\r\n\theight: 64rpx;\r\n\tborder-radius: 12rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 24rpx;\r\n\r\n\t&.feedback-icon {\r\n\t\tbackground: rgba(102, 126, 234, 0.1);\r\n\t}\r\n\r\n\t&.privacy-icon {\r\n\t\tbackground: rgba(16, 185, 129, 0.1);\r\n\t}\r\n\r\n\t&.agreement-icon {\r\n\t\tbackground: rgba(245, 158, 11, 0.1);\r\n\t}\r\n}\r\n\r\n.service-content {\r\n\tflex: 1;\r\n\r\n\t.service-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #1a1a1a;\r\n\t\tmargin-bottom: 4rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.service-desc {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tdisplay: block;\r\n\t}\r\n}\r\n\r\n/* 退出登录 */\r\n.logout-section {\r\n\tmargin-top: 40rpx;\r\n}\r\n\r\n.logout-btn {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 32rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n\ttransition: all 0.3s ease;\r\n\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbackground: #fef2f2;\r\n\t}\r\n\r\n\t.logout-text {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #ef4444;\r\n\t\tmargin-left: 16rpx;\r\n\t}\r\n}\r\n\r\n/* 响应式适配 */\r\n@media (max-width: 750rpx) {\r\n\t.menu-grid {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tgap: 16rpx;\r\n\t\tpadding: 24rpx;\r\n\t}\r\n\r\n\t.menu-item {\r\n\t\tpadding: 24rpx 16rpx;\r\n\t}\r\n\r\n\t.menu-icon {\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=624165d7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=624165d7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754364581160\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}