<view class="publish-container"><view class="page-header"><view class="header-content"><text class="header-title">🏠 发布房源</text><text class="header-subtitle">填写详细信息，让租客更容易找到您的房源</text></view></view><view class="form-container"><view class="form-card modern-card"><view class="card-header gradient-header"><view class="header-icon">🏘️</view><view class="header-text"><text class="card-title">基本信息</text><text class="card-subtitle">选择房源的基本属性</text></view></view><view class="card-content"><view class="form-group modern-group"><view class="group-header"><text class="group-icon">🏠</text><text class="group-label">房型类型</text></view><view data-event-opts="{{[['tap',[['showTypePicker',['$event']]]]]}}" class="selector-container" bindtap="__e"><view class="selector-display"><view class="selector-icon">{{$root.m0}}</view><text class="selector-text">{{form.type||'请选择房型类型'}}</text><text class="selector-arrow">▼</text></view></view></view><view class="form-group modern-group"><view class="group-header"><text class="group-icon">🧭</text><text class="group-label">床位朝向</text></view><view data-event-opts="{{[['tap',[['showDirectionPicker',['$event']]]]]}}" class="selector-container" bindtap="__e"><view class="selector-display"><view class="selector-icon">{{$root.m1}}</view><text class="selector-text">{{form.direction||'请选择床位朝向'}}</text><text class="selector-arrow">▼</text></view></view></view><view class="form-group modern-group"><view class="group-header"><text class="group-icon">🛗</text><text class="group-label">电梯</text></view><view class="option-grid binary-grid"><block wx:for="{{liftList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['option-item','modern-option','binary-option',(form.isLift===item.name)?'active':'']}}" bindtap="__e"><view class="option-icon">{{item.name==='是'?'✅':'❌'}}</view><text class="option-text">{{item.name}}</text><block wx:if="{{form.isLift===item.name}}"><view class="option-check">✓</view></block></view></block></view></view><view class="form-group modern-group"><view class="group-header"><text class="group-icon">🚗</text><text class="group-label">停车位</text></view><view class="option-grid binary-grid"><block wx:for="{{parkingList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item})}}" class="{{['option-item','modern-option','binary-option',(form.isParking===item.name)?'active':'']}}" bindtap="__e"><view class="option-icon">{{item.name==='是'?'✅':'❌'}}</view><text class="option-text">{{item.name}}</text><block wx:if="{{form.isParking===item.name}}"><view class="option-check">✓</view></block></view></block></view></view></view></view><view class="form-card modern-card"><view class="card-header gradient-header config-header"><view class="header-icon">🛋️</view><view class="header-text"><text class="card-title">房屋配置</text><text class="card-subtitle">选择房源提供的设施配置</text></view></view><view class="card-content"><view class="config-grid"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleConfig',[index]]]]]}}" class="{{['config-item','modern-config',(item.$orig.checked)?'checked':'']}}" bindtap="__e"><view class="config-icon-wrapper"><text class="config-icon">{{item.m2}}</text><block wx:if="{{item.$orig.checked}}"><view class="config-check"><text class="check-icon">✓</text></view></block></view><text class="config-text">{{item.$orig.name}}</text></view></block></view></view></view><view class="form-card"><view class="card-header"><text class="card-title">🌍 周边设施</text></view><view class="card-content"><view class="checkbox-grid"><block wx:for="{{envList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toggleEnv',[index]]]]]}}" class="{{['checkbox-item',(item.checked)?'checked':'']}}" bindtap="__e"><text class="checkbox-icon">{{item.checked?'✓':''}}</text><text class="checkbox-text">{{item.name}}</text></view></block></view></view></view><view class="form-card"><view class="card-header"><text class="card-title">📝 房源详情</text></view><view class="card-content"><view class="input-group"><text class="input-label">房源标题</text><input class="custom-input" placeholder="请输入房源标题" maxlength="50" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['form']]]]]}}" value="{{form.title}}" bindinput="__e"/></view><view class="input-group"><text class="input-label">房源简介</text><textarea class="custom-textarea" placeholder="请详细描述房源特色、周边环境等信息" maxlength="500" data-event-opts="{{[['input',[['__set_model',['$0','info','$event',[]],['form']]]]]}}" value="{{form.info}}" bindinput="__e"></textarea></view><view class="input-row"><view class="input-group half"><text class="input-label">租金(元/月)</text><input class="custom-input" type="number" placeholder="0" data-event-opts="{{[['input',[['__set_model',['$0','rent','$event',[]],['form']]]]]}}" value="{{form.rent}}" bindinput="__e"/></view><view class="input-group half"><text class="input-label">面积(m²)</text><input class="custom-input" type="number" placeholder="0" data-event-opts="{{[['input',[['__set_model',['$0','area','$event',[]],['form']]]]]}}" value="{{form.area}}" bindinput="__e"/></view></view><view class="input-group"><text class="input-label">楼层</text><input class="custom-input" placeholder="如：3/6层" data-event-opts="{{[['input',[['__set_model',['$0','floor','$event',[]],['form']]]]]}}" value="{{form.floor}}" bindinput="__e"/></view></view></view><view class="form-card"><view class="card-header"><text class="card-title">📞 联系信息</text></view><view class="card-content"><view class="input-group"><text class="input-label">联系人</text><input class="custom-input" placeholder="请输入联系人姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" value="{{form.name}}" bindinput="__e"/></view><view class="input-group"><text class="input-label">联系方式</text><view class="phone-input-container"><input class="custom-input phone-input" type="number" placeholder="请输入手机号" data-event-opts="{{[['input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" value="{{form.mobile}}" bindinput="__e"/><block wx:if="{{userInfo.mobile=='未绑定'||userInfo.mobile==null||userInfo.mobile==''}}"><button class="quick-input-btn" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">一键输入</button></block></view></view><view class="input-group"><text class="input-label">微信号</text><input class="custom-input" placeholder="请输入微信号（选填）" data-event-opts="{{[['input',[['__set_model',['$0','weixin','$event',[]],['form']]]]]}}" value="{{form.weixin}}" bindinput="__e"/></view><view class="input-group"><text class="input-label">房源地址</text><view data-event-opts="{{[['tap',[['openMapSelector',['$event']]]]]}}" class="address-selector" bindtap="__e"><view class="address-content"><text class="{{['address-text',(!form.address)?'placeholder':'']}}">{{''+(form.address||'点击选择地址')+''}}</text><block wx:if="{{form.latitude&&form.longitude}}"><text class="address-coordinates">{{'经纬度: '+$root.g0+", "+$root.g1+''}}</text></block></view><text class="address-icon">📍</text></view><view class="manual-input-option"><text data-event-opts="{{[['tap',[['toggleManualInput',['$event']]]]]}}" class="manual-link" bindtap="__e">{{''+(showManualInput?'使用地图选择':'手动输入地址')+''}}</text></view><block wx:if="{{showManualInput}}"><view class="manual-address-input"><input class="custom-input" placeholder="请输入详细地址（如：北京市朝阳区xxx小区x号楼）" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form']],['clearCoordinates',['$event']]]]]}}" value="{{form.address}}" bindinput="__e"/></view></block><view class="address-tip"><text class="tip-text">💡 建议使用地图选择获得精确位置，或手动输入详细地址</text></view></view></view></view><view class="form-card"><view class="card-header"><text class="card-title">📷 房源图片</text><text class="card-subtitle">最多上传9张图片</text></view><view class="card-content"><view class="image-upload-container"><block wx:if="{{$root.g2===0}}"><view data-event-opts="{{[['tap',[['upImage',['$event']]]]]}}" class="upload-placeholder" bindtap="__e"><text class="upload-icon">📷</text><text class="upload-text">点击上传图片</text><text class="upload-hint">支持多张图片上传</text></view></block><block wx:else><view class="image-grid"><view data-event-opts="{{[['tap',[['upImage',['$event']]]]]}}" class="image-item add-image" bindtap="__e"><text class="add-icon">+</text></view><block wx:for="{{form.pics}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="image-item"><image class="uploaded-image" src="{{item}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="image-delete" bindtap="__e">×</view></view></block></view></block><block wx:if="{{$root.g3>0}}"><view data-event-opts="{{[['tap',[['reset',['$event']]]]]}}" class="clear-all-btn" bindtap="__e">清空所有图片</view></block></view></view></view><view class="submit-container"><button data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="submit-btn" bindtap="__e"><text class="submit-text">发布房源</text></button></view></view></view>