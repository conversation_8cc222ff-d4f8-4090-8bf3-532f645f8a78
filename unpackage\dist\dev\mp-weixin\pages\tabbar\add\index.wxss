@charset "UTF-8";
page {
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
}
.publish-container {
  min-height: 100vh;
  padding-bottom: 40rpx;
}
/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx 40rpx;
  margin-bottom: 20rpx;
}
.page-header .header-content {
  text-align: center;
}
.page-header .header-content .header-title {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 12rpx;
}
.page-header .header-content .header-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
/* 表单容器 */
.form-container {
  padding: 0 24rpx;
}
/* 卡片样式 */
.form-card {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.form-card .card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #e2e8f0;
}
.form-card .card-header .card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}
.form-card .card-header .card-subtitle {
  font-size: 24rpx;
  color: #64748b;
  margin-left: 16rpx;
}
.form-card .card-content {
  padding: 32rpx;
}
/* 表单组样式 */
.form-group {
  margin-bottom: 32rpx;
}
.form-group:last-child {
  margin-bottom: 0;
}
.form-group .group-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}
/* 单选框网格 */
.radio-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.radio-grid .radio-item {
  flex: 1;
  min-width: 120rpx;
  padding: 16rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}
.radio-grid .radio-item:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.radio-grid .radio-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.radio-grid .radio-item.active .radio-text {
  color: #ffffff;
  font-weight: 600;
}
.radio-grid .radio-item .radio-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #64748b;
  transition: color 0.3s ease;
}
/* 复选框网格 */
.checkbox-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.checkbox-grid .checkbox-item {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-width: 120rpx;
  cursor: pointer;
}
.checkbox-grid .checkbox-item:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.checkbox-grid .checkbox-item.checked {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.checkbox-grid .checkbox-item.checked .checkbox-text {
  color: #ffffff;
  font-weight: 600;
}
.checkbox-grid .checkbox-item.checked .checkbox-icon {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}
.checkbox-grid .checkbox-item .checkbox-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.checkbox-grid .checkbox-item .checkbox-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}
/* 输入框样式 */
.input-group {
  margin-bottom: 32rpx;
}
.input-group.half {
  flex: 1;
}
.input-group:last-child {
  margin-bottom: 0;
}
.input-group .input-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
}
.input-row {
  display: flex;
  gap: 20rpx;
}
.custom-input {
  width: 100%;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1e293b;
  transition: all 0.3s ease;
}
.custom-input:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.custom-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1e293b;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
}
.custom-textarea:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
/* 手机号输入容器 */
.phone-input-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}
.phone-input-container .phone-input {
  flex: 1;
}
.phone-input-container .quick-input-btn {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
}
/* 地址选择器 */
.address-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-height: 80rpx;
}
.address-selector:active {
  background: #e2e8f0;
}
.address-selector .address-content {
  flex: 1;
}
.address-selector .address-content .address-text {
  display: block;
  font-size: 28rpx;
  color: #1e293b;
  line-height: 1.4;
  margin-bottom: 4rpx;
}
.address-selector .address-content .address-text.placeholder {
  color: #9ca3af;
}
.address-selector .address-content .address-coordinates {
  display: block;
  font-size: 22rpx;
  color: #64748b;
  opacity: 0.8;
}
.address-selector .address-icon {
  font-size: 32rpx;
  margin-left: 16rpx;
  color: #667eea;
}
/* 手动输入提示 */
.manual-input-tip {
  margin-top: 12rpx;
  text-align: center;
}
.manual-input-tip .tip-text {
  font-size: 24rpx;
  color: #667eea;
  text-decoration: underline;
}
.manual-input-option {
  margin-top: 16rpx;
  text-align: center;
}
.manual-input-option .manual-link {
  font-size: 26rpx;
  color: #007AFF;
  text-decoration: underline;
  padding: 12rpx;
}
.manual-input-option .manual-link:active {
  opacity: 0.7;
}
.manual-address-input {
  margin-top: 16rpx;
}
/* 地址提示样式 */
.address-tip {
  margin-top: 12rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #0ea5e9;
}
.address-tip .tip-text {
  font-size: 24rpx;
  color: #0369a1;
  line-height: 1.4;
}
/* 图片上传样式 */
.image-upload-container .upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 3rpx dashed #cbd5e1;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.image-upload-container .upload-placeholder:active {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}
.image-upload-container .upload-placeholder .upload-icon {
  font-size: 80rpx;
  margin-bottom: 16rpx;
}
.image-upload-container .upload-placeholder .upload-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #475569;
  margin-bottom: 8rpx;
}
.image-upload-container .upload-placeholder .upload-hint {
  font-size: 24rpx;
  color: #64748b;
}
.image-upload-container .image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.image-upload-container .image-grid .image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.image-upload-container .image-grid .image-item.add-image {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 3rpx dashed #cbd5e1;
}
.image-upload-container .image-grid .image-item.add-image .add-icon {
  font-size: 60rpx;
  color: #64748b;
}
.image-upload-container .image-grid .image-item .uploaded-image {
  width: 100%;
  height: 100%;
}
.image-upload-container .image-grid .image-item .image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(239, 68, 68, 0.9);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}
.image-upload-container .clear-all-btn {
  margin-top: 24rpx;
  padding: 16rpx 32rpx;
  background: #fee2e2;
  color: #dc2626;
  border-radius: 12rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.image-upload-container .clear-all-btn:active {
  background: #fecaca;
}
/* 提交按钮 */
.submit-container {
  padding: 40rpx 24rpx;
}
.submit-container .submit-btn {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.submit-container .submit-btn:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.submit-container .submit-btn .submit-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}
/* ========== 现代化设计样式 ========== */
/* 现代化卡片增强 */
.modern-card {
  border: 1rpx solid #f1f5f9;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}
/* 渐变头部 */
.gradient-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx !important;
}
.gradient-header .header-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.gradient-header .header-text {
  flex: 1;
}
.gradient-header .header-text .card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white !important;
  margin-bottom: 8rpx;
}
.gradient-header .header-text .card-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-left: 0 !important;
}
.config-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
/* 现代化表单组 */
.modern-group {
  margin-bottom: 40rpx;
}
.modern-group .group-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.modern-group .group-header .group-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.modern-group .group-header .group-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0 !important;
}
/* 现代化选项网格 */
.option-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.binary-grid {
  gap: 20rpx;
}
/* 现代化选项项 */
.modern-option {
  position: relative;
  flex: 1;
  min-width: 140rpx;
  padding: 20rpx 16rpx;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.modern-option .option-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}
.modern-option .option-text {
  font-size: 26rpx;
  color: #475569;
  font-weight: 500;
}
.modern-option .option-check {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
}
.modern-option:hover {
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.modern-option.active {
  background: linear-gradient(145deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
  color: #1e40af;
  -webkit-transform: translateY(-2rpx);
          transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.2);
}
.modern-option.active .option-text {
  color: #1e40af;
  font-weight: 600;
}
.binary-option {
  flex: 0 0 calc(50% - 10rpx);
  min-width: auto;
}
/* 现代化配置网格 */
.config-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
/* 现代化配置项 */
.modern-config {
  position: relative;
  flex: 0 0 calc(25% - 12rpx);
  min-width: 120rpx;
  padding: 24rpx 16rpx;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 20rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.modern-config .config-icon-wrapper {
  position: relative;
  margin-bottom: 12rpx;
}
.modern-config .config-icon-wrapper .config-icon {
  font-size: 40rpx;
  display: block;
}
.modern-config .config-icon-wrapper .config-check {
  position: absolute;
  top: -8rpx;
  right: 50%;
  -webkit-transform: translateX(50%);
          transform: translateX(50%);
  width: 28rpx;
  height: 28rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
}
.modern-config .config-icon-wrapper .config-check .check-icon {
  font-size: 16rpx;
  color: white;
  font-weight: bold;
}
.modern-config .config-text {
  font-size: 24rpx;
  color: #475569;
  font-weight: 500;
}
.modern-config:hover {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.1);
}
.modern-config.checked {
  background: linear-gradient(145deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #10b981;
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(16, 185, 129, 0.15);
}
.modern-config.checked .config-text {
  color: #047857;
  font-weight: 600;
}
/* 响应式适配 */
@media (max-width: 750rpx) {
.modern-config {
    flex: 0 0 calc(33.333% - 11rpx);
}
}
@media (max-width: 600rpx) {
.modern-config {
    flex: 0 0 calc(50% - 8rpx);
}
}
