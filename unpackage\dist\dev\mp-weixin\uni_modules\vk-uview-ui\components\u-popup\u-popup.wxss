.u-drawer.data-v-27202335 {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.u-drawer-content.data-v-27202335 {
  display: block;
  position: absolute;
  z-index: 1003;
  transition: all 0.25s linear;
}
.u-drawer__scroll-view.data-v-27202335 {
  width: 100%;
  height: 100%;
}
.u-drawer-left.data-v-27202335 {
  top: 0;
  bottom: 0;
  left: 0;
  background-color: #ffffff;
}
.u-drawer-right.data-v-27202335 {
  right: 0;
  top: 0;
  bottom: 0;
  background-color: #ffffff;
}
.u-drawer-top.data-v-27202335 {
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
}
.u-drawer-bottom.data-v-27202335 {
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
}
.u-drawer-center.data-v-27202335 {
  display: flex;
  flex-direction: row;
  flex-direction: column;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  justify-content: center;
  align-items: center;
  opacity: 0;
  z-index: 99999;
}
.u-mode-center-box.data-v-27202335 {
  min-width: 100rpx;
  min-height: 100rpx;
  display: block;
  position: relative;
  background-color: #ffffff;
}
.u-drawer-content-visible.u-drawer-center.data-v-27202335 {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}
.u-animation-zoom.data-v-27202335 {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.u-drawer-content-visible.data-v-27202335 {
  -webkit-transform: translate3D(0px, 0px, 0px) !important;
          transform: translate3D(0px, 0px, 0px) !important;
}
.u-close.data-v-27202335 {
  position: absolute;
  z-index: 3;
}
.u-close--top-left.data-v-27202335 {
  top: 30rpx;
  left: 30rpx;
}
.u-close--top-right.data-v-27202335 {
  top: 30rpx;
  right: 30rpx;
}
.u-close--bottom-left.data-v-27202335 {
  bottom: 30rpx;
  left: 30rpx;
}
.u-close--bottom-right.data-v-27202335 {
  right: 30rpx;
  bottom: 30rpx;
}
