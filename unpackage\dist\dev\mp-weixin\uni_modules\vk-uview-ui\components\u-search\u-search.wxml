<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-search data-v-08c1dccb" style="{{'margin:'+(margin)+';'}}" bindtap="__e"><view class="u-content data-v-08c1dccb" style="{{'background-color:'+(bgColor)+';'+('border-radius:'+(shape=='round'?'100rpx':'10rpx')+';')+('border:'+(borderStyle)+';')+('height:'+(height+'rpx')+';')}}"><view class="u-icon-wrap data-v-08c1dccb"><u-icon class="u-clear-icon data-v-08c1dccb" vue-id="0f7a3d9b-1" size="{{30}}" name="{{searchIcon}}" color="{{searchIconColor?searchIconColor:color}}" bind:__l="__l"></u-icon></view><input class="u-input data-v-08c1dccb" style="{{$root.s0}}" confirm-type="search" disabled="{{disabled}}" focus="{{focus}}" maxlength="{{maxlength}}" placeholder-class="u-placeholder-class" placeholder="{{placeholder}}" placeholder-style="{{'color: '+placeholderColor}}" type="text" data-event-opts="{{[['blur',[['blur',['$event']]]],['confirm',[['search',['$event']]]],['input',[['inputChange',['$event']]]],['focus',[['getFocus',['$event']]]]]}}" value="{{valueCom}}" bindblur="__e" bindconfirm="__e" bindinput="__e" bindfocus="__e"/><block wx:if="{{keyword&&clearabled&&focused}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="u-close-wrap data-v-08c1dccb" bindtap="__e"><u-icon class="u-clear-icon data-v-08c1dccb" vue-id="0f7a3d9b-2" name="close-circle-fill" size="34" color="#c0c4cc" bind:__l="__l"></u-icon></view></block></view><view data-event-opts="{{[['tap',[['custom',['$event']]]]]}}" class="{{['u-action','data-v-08c1dccb',showActionBtn||show?'u-action-active':'']}}" style="{{$root.s1}}" catchtap="__e">{{actionText}}</view></view>