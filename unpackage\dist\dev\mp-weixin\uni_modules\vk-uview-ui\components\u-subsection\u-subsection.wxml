<view class="u-subsection data-v-2425c72a" style="{{$root.s0}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['click',[index]]]]]}}" class="{{['u-item','u-line-1','data-v-2425c72a',item.m0,'u-item-'+index]}}" style="{{item.s1}}" bindtap="__e"><view class="u-item-text u-line-1 data-v-2425c72a" style="{{item.s2}}">{{item.$orig.name}}</view><block wx:if="{{item.$orig.num>0}}"><u-badge vue-id="{{'60921c0a-1-'+index}}" count="{{item.$orig.num}}" offset="{{offset}}" size="mini" class="data-v-2425c72a" bind:__l="__l"></u-badge></block></view></block><view class="u-item-bg data-v-2425c72a" style="{{$root.s3}}"></view></view>