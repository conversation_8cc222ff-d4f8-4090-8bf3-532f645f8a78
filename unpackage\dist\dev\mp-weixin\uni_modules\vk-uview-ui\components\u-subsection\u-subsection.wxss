.u-subsection.data-v-2425c72a {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
  position: relative;
}
.u-item.data-v-2425c72a {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #303133;
  padding: 0 6rpx;
  position: relative;
}
.u-item-bg.data-v-2425c72a {
  background-color: #2979ff;
  position: absolute;
  z-index: -1;
}
.u-none-border-right.data-v-2425c72a {
  border-right: none !important;
}
.u-item-first.data-v-2425c72a {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.u-item-last.data-v-2425c72a {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}
.u-item-text.data-v-2425c72a {
  transition: all 0.35s;
  color: #303133;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  z-index: 3;
}
